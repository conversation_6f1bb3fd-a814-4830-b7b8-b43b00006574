"use client"

import { useState } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { AuthorProfileLink } from "@/components/AuthorProfileLink"


interface Book {
  id: string
  title: string
  description: string
  cover_image_url: string
  price_amount: number
  genre: string
  book_type?: string
  average_rating?: number
  review_count?: number
  sales_count?: number
  created_at: string
  slug?: string
  author_name?: string
  users: {
    name: string
    avatar?: string
    profile_picture_url?: string
  }
}

interface BookCardProps {
  book: Book
  showShareButton?: boolean
  priority?: boolean
  onClick?: (bookId: string) => void
  showRankBadge?: boolean
  rank?: number
}

export function BookCard({ book, showShareButton = true, priority = false, showRankBadge = false, rank }: BookCardProps) {
  const [imageError, setImageError] = useState(false)

  const formatPrice = (cents: number | null) => {
    if (cents === 0 || cents === null) return "Free"
    return `$${(cents / 100).toFixed(2)}`
  }

  const renderPenRating = (rating: number = 0, reviewCount: number = 0) => {
    if (reviewCount === 0 || !rating) {
      return (
        <div className="text-xs text-gray-400 truncate">
          <span>No ratings yet</span>
        </div>
      )
    }

    return (
      <div className="flex items-center gap-1 text-xs text-gray-600">
        <span className="text-yellow-500" style={{ filter: 'drop-shadow(0 0 2px rgba(255, 215, 0, 0.8))' }}>🖊️</span>
        <span>{rating.toFixed(1)}</span>
        <span className="text-gray-400">({reviewCount})</span>
      </div>
    )
  }

  const getBookUrl = () => {
    return book.slug ? `/books/${book.slug}` : `/books/${book.id}`
  }



  return (
    <Card className="group hover:shadow-xl transition-all duration-500 hover:scale-[1.02] cursor-pointer h-full relative border-gray-100 hover:border-purple-200 bg-white hover:bg-gradient-to-br hover:from-white hover:to-purple-50/20">

      <Link href={getBookUrl()}>
        {/* Book Cover - Full visibility without cropping */}
        <div className="aspect-[3/4] relative overflow-hidden rounded-t-lg bg-white border border-gray-100">
          {book.cover_image_url && !imageError ? (
            <img
              src={book.cover_image_url}
              alt={book.title}
              className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-300 bg-gradient-to-br from-purple-50 to-blue-50"
              onError={() => setImageError(true)}
              loading={priority ? "eager" : "lazy"}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-4xl bg-gradient-to-br from-purple-100 to-blue-100">
              📖
            </div>
          )}



        </div>

        <CardContent className="p-4 min-h-[182px] flex flex-col bg-white overflow-visible">
          {/* Rank badge row (Amazon style) */}
          {showRankBadge && (
            <div className="mb-2">
              <span className="inline-flex items-center gap-1 px-2 py-0.5 rounded-full bg-amber-100 text-amber-800 text-[11px] font-semibold border border-amber-200">
                <span>#{rank}</span>
                <span className="opacity-80">Bestseller</span>
              </span>
            </div>
          )}

          {/* Title - mobile-safe line height */}
          <div className="mb-2 min-h-[1.75rem]">
            <h3 className="font-semibold text-gray-900 group-hover:text-purple-600 transition-colors text-sm leading-snug line-clamp-2">
              {book.title}
            </h3>
          </div>

          {/* Author - text only: by {name} */}
          <div className="h-[18px] flex items-center gap-1.5 mb-2">
            <span className="text-[11px] text-gray-500 mr-1">by</span>
            <AuthorProfileLink userId={(book as any).user_id} userName={book.author_name || book.users.name} className="text-xs text-gray-700 hover:text-purple-600" />
          </div>

          {/* Description - Fixed height, exactly 2 lines maximum */}
          <div className="h-[32px] mb-2 flex items-start">
            {book.description && (
              <p className="text-xs text-gray-600 line-clamp-2 leading-[16px] overflow-hidden">
                {book.description}
              </p>
            )}
          </div>

          {/* Genre - Only show main genre, skip "Other" */}
          <div className="h-[20px] mb-2 flex items-start">
            {book.genre && book.genre.toLowerCase() !== 'other' && (
              <span className="inline-block px-2 py-0.5 bg-purple-100 text-purple-700 text-[10px] rounded font-medium leading-none">
                {book.genre.replace('_', ' ')}
              </span>
            )}
          </div>

          {/* Dedicated Rating & Sales Section */}
          <div className="mt-auto">
            <div className="border-t border-gray-100 pt-2">
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center min-w-0 flex-1">
                  {renderPenRating(book.average_rating || 0, book.review_count || 0)}
                </div>
                {(book.sales_count || 0) > 0 && (
                  <div className="text-gray-500 font-medium ml-2 flex-shrink-0">
                    {book.sales_count} sales
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Link>
    </Card>
  )
}
