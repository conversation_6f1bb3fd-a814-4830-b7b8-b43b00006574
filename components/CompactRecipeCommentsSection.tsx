"use client"

import { useState } from "react"
import { RecipeCommentsSection } from "./RecipeCommentsSection"

interface CompactRecipeCommentsSectionProps {
  recipeId: string
  canComment: boolean
  userId?: string
  isOpen: boolean
  onToggle: () => void
  commentCount?: number
}

export function CompactRecipeCommentsSection({
  recipeId,
  canComment,
  userId,
  isOpen,
  onToggle,
  commentCount = 0,
}: CompactRecipeCommentsSectionProps) {
  return (
    <div className="border-t border-gray-100">
      <button
        onClick={onToggle}
        className="w-full px-4 py-2 text-left text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition-colors flex items-center justify-between"
      >
        <span className="flex items-center gap-2">💬 {commentCount > 0 ? `${commentCount} comments` : 'Comments'}</span>
        <span className="text-xs">{isOpen ? '▲' : '▼'}</span>
      </button>

      {isOpen && (
        <div className="px-4 pb-4 bg-gray-50">
          <RecipeCommentsSection
            recipeId={recipeId}
            canComment={canComment}
            userId={userId}
            maxDepth={3}
            isCompact={true}
          />
        </div>
      )}
    </div>
  )
}

