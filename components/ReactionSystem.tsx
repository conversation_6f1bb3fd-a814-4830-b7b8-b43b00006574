"use client"

import { useState, useEffect, useRef } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { REACTION_EMOJIS, getReactionDisplay } from "@/lib/reaction-utils"

interface ReactionType {
  id: string
  emoji: string
  label: string
}

interface ReactionSystemProps {
  contentId: string
  contentType: 'diary' | 'audio' | 'book' | 'recipe' | 'book_audio_post' | 'book_audio_reply' | 'comment'
  currentUserId?: string
  initialReactions?: Record<string, number>
  userReaction?: string | null
  onReactionUpdate?: (reactions: Record<string, number>, userReaction: string | null) => void
  bookId?: string // Required for book audio content types
  variant?: 'default' | 'fb'
}

const REACTION_TYPES: ReactionType[] = [
  { id: 'love', emoji: REACTION_EMOJIS.love, label: 'Love' },
  { id: 'fire', emoji: REACTION_EMOJIS.fire, label: 'Fire' },
  { id: 'smile', emoji: REACTION_EMOJIS.smile, label: 'Happy' },
  { id: 'cry', emoji: REACTION_EMOJIS.cry, label: 'Sad' },
  { id: 'broken', emoji: REACTION_EMOJIS.broken, label: 'Heartbroken' },
  { id: 'wow', emoji: REACTION_EMOJIS.wow, label: 'Wow' },
  { id: 'laugh', emoji: REACTION_EMOJIS.laugh, label: 'Laugh' }
]

export function ReactionSystem({
  contentId,
  contentType,
  currentUserId,
  initialReactions = {},
  userReaction,
  onReactionUpdate,
  bookId,
  variant = 'default'
}: ReactionSystemProps) {
  const [reactions, setReactions] = useState<Record<string, number>>(initialReactions)
  const [currentUserReaction, setCurrentUserReaction] = useState<string | null>(userReaction || null)

  // Update state when props change (important for when data is loaded asynchronously)
  useEffect(() => {
    setReactions(initialReactions)
    setCurrentUserReaction(userReaction || null)
  }, [initialReactions, userReaction])
  const [showPicker, setShowPicker] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [pickerPosition, setPickerPosition] = useState({ top: 0, left: 0 })
  const pickerRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)
  const supabase = createSupabaseClient()

  // Close picker when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (pickerRef.current && !pickerRef.current.contains(event.target as Node)) {
        setShowPicker(false)
      }
    }

    if (showPicker) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showPicker])

  const totalReactions = Object.values(reactions).reduce((sum, count) => sum + count, 0)
  const userReactionEmoji = currentUserReaction ? REACTION_TYPES.find(r => r.id === currentUserReaction)?.emoji : null

  // Debug logging to help troubleshoot
  useEffect(() => {
    console.log('ReactionSystem Debug:', {
      contentId,
      currentUserReaction,
      userReactionEmoji,
      userReaction: userReaction,
      reactions,
      totalReactions
    })
  }, [contentId, currentUserReaction, userReactionEmoji, userReaction, reactions, totalReactions])

  // Get the most popular reaction for display
  const mostPopularReaction = totalReactions > 0
    ? Object.entries(reactions).reduce((a, b) => a[1] > b[1] ? a : b)[0]
    : null
  const mostPopularEmoji = mostPopularReaction ? REACTION_EMOJIS[mostPopularReaction] || '❤️' : null

  const handleReactionClick = async (reactionId: string) => {
    if (!currentUserId) {
      alert('Please log in to react')
      return
    }

    setIsLoading(true)
    setShowPicker(false)

    try {
      // Use API endpoints for book audio content types and comments
      if (contentType === 'book_audio_post' || contentType === 'book_audio_reply' || contentType === 'comment') {
        let endpoint: string

        if (contentType === 'comment') {
          endpoint = `/api/comments/${contentId}/reactions`
        } else {
          if (!bookId) {
            throw new Error('bookId is required for book audio content')
          }
          endpoint = contentType === 'book_audio_post'
            ? `/api/books/${bookId}/audio/posts/${contentId}/reactions`
            : `/api/books/${bookId}/audio/replies/${contentId}/reactions`
        }

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ reactionType: reactionId })
        })

        if (!response.ok) {
          throw new Error('Failed to update reaction')
        }

        const { reactionType } = await response.json()

        // Fetch updated reaction counts
        const statusResponse = await fetch(endpoint)
        if (statusResponse.ok) {
          const { reactions: updatedReactions, userReaction: updatedUserReaction } = await statusResponse.json()
          setReactions(updatedReactions)
          setCurrentUserReaction(updatedUserReaction)
          onReactionUpdate?.(updatedReactions, updatedUserReaction)
        }
      } else {
        // Original logic for other content types
        const wasCurrentReaction = currentUserReaction === reactionId
        let newReactions = { ...reactions }
        let newUserReaction: string | null = null

        // Remove old reaction if exists
        if (currentUserReaction) {
          await supabase
            .from('reactions')
            .delete()
            .eq(getContentColumn(), contentId)
            .eq('user_id', currentUserId)

          // Decrease old reaction count
          newReactions[currentUserReaction] = Math.max(0, (newReactions[currentUserReaction] || 0) - 1)
        }

        // Add new reaction if it's different from current
        if (!wasCurrentReaction) {
          const insertData: any = {
            user_id: currentUserId,
            reaction_type: reactionId
          }
          insertData[getContentColumn()] = contentId

          await supabase
            .from('reactions')
            .insert(insertData)

          // Increase new reaction count
          newReactions[reactionId] = (newReactions[reactionId] || 0) + 1
          newUserReaction = reactionId
        }

        setReactions(newReactions)
        setCurrentUserReaction(newUserReaction)
        onReactionUpdate?.(newReactions, newUserReaction)
      }

    } catch (error) {
      console.error('Error updating reaction:', error)
      alert('Failed to update reaction')
    } finally {
      setIsLoading(false)
    }
  }

  const getContentColumn = () => {
    switch (contentType) {
      case 'diary': return 'diary_entry_id'
      case 'audio': return 'audio_post_id'
      case 'book': return 'book_id'
      case 'recipe': return 'recipe_id'
      case 'book_audio_post': return 'book_audio_post_id'
      case 'book_audio_reply': return 'book_audio_reply_id'
      case 'comment': return 'comment_id'
      default: return 'diary_entry_id'
    }
  }

  const handleHeartClick = () => {
    if (!currentUserId) {
      alert('Please log in to react')
      return
    }

    if (!showPicker && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect()
      setPickerPosition({
        top: rect.top - 80, // Position above the button
        left: rect.left
      })
    }

    setShowPicker(!showPicker)
  }

  return (
    <div className="relative" ref={pickerRef}>
      {/* Main reaction button */}
      <button
        ref={buttonRef}
        onClick={handleHeartClick}
        disabled={isLoading}
        className={`group flex items-center gap-2 transition-all duration-200 disabled:opacity-50 ${variant === 'fb' ? 'justify-center text-gray-600 hover:text-blue-600' : 'text-gray-600 hover:text-gray-800'}`}
      >
        {/* Reaction icon - shows user's reaction if they have one, otherwise most popular reaction or hollow heart */}
        <div className="relative">
          {userReactionEmoji ? (
            <span className="text-lg">{userReactionEmoji}</span>
          ) : totalReactions > 0 && mostPopularEmoji ? (
            <span className="text-lg">{mostPopularEmoji}</span>
          ) : (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 000-6.364 4.5 4.5 0 00-6.364 0L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          )}
        </div>

        {/* Label or count */}
        {variant === 'fb' ? (
          <span className="text-sm font-medium">React</span>
        ) : (
          totalReactions > 0 && (
            <span className="text-sm font-medium text-gray-700">
              {totalReactions}
            </span>
          )
        )}
      </button>

      {/* Reaction picker popup - using fixed positioning to avoid clipping */}
      {showPicker && (
        <div
          className="fixed bg-white border border-gray-200 rounded-lg shadow-lg p-2 flex gap-1 min-w-max"
          style={{
            top: `${pickerPosition.top}px`,
            left: `${pickerPosition.left}px`,
            zIndex: 9999
          }}
          ref={pickerRef}
        >
          {REACTION_TYPES.map((reaction) => (
            <button
              key={reaction.id}
              onClick={() => handleReactionClick(reaction.id)}
              disabled={isLoading}
              className={`flex flex-col items-center p-2 rounded-lg hover:bg-gray-100 transition-colors min-w-[50px] disabled:opacity-50 ${
                currentUserReaction === reaction.id
                  ? 'bg-blue-50 border border-blue-200'
                  : ''
              }`}
              title={reaction.label}
            >
              <span className="text-xl mb-1">{reaction.emoji}</span>
              <span className="text-xs text-gray-600 font-medium">
                {reactions[reaction.id] || 0}
              </span>
            </button>
          ))}
        </div>
      )}
    </div>
  )
}
