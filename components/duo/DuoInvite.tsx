"use client"

import { useEffect, useMemo, useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

export function DuoInvite({ duoPostId, onFriendSelected }: { duoPostId: string; onFriendSelected?: (u: { id: string; name: string } | null) => void }) {
  const supabase = createSupabaseClient()
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<Array<{ id: string; name: string; profile_picture_url?: string | null; avatar?: string | null }>>([])
  const [selected, setSelected] = useState<{ id: string; name: string } | null>(null)
  const [loading, setLoading] = useState(false)
  const [status, setStatus] = useState<string | null>(null)
  const [reserveExclusive, setReserveExclusive] = useState(false)
  const [quickPicks, setQuickPicks] = useState<Array<{ id: string; name: string; profile_picture_url?: string | null; avatar?: string | null }>>([])

  const respondUrl = useMemo(() => {
    if (typeof window !== 'undefined') return `${window.location.origin}/duo/respond/${duoPostId}`
    return `/duo/respond/${duoPostId}`
  }, [duoPostId])

  useEffect(() => {
    let cancelled = false
    async function run() {
      if (!query) { setResults([]); return }
      const { data } = await supabase
        .from('users' as any)
        .select('id, name, profile_picture_url, avatar')
        .ilike('name', `%${query}%`)
        .limit(8)
      if (!cancelled) setResults(data || [])
    }
    const t = setTimeout(run, 150)
    return () => { cancelled = true; clearTimeout(t) }

  // Load quick picks: people you follow (top 6)
  useEffect(() => {
    let cancelled = false
    async function loadQuickPicks() {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) return
        const { data } = await supabase
          .from('follows')
          .select(`
            writer_id,
            users:users!follows_writer_id_fkey(id, name, profile_picture_url, avatar)
          `)
          .eq('follower_id', user.id)
          .limit(12)
        if (!cancelled) {
          const picks = (data || []).map((r: any) => r.users).filter(Boolean).slice(0, 6)
          setQuickPicks(picks)
        }
      } catch {}
    }
    loadQuickPicks()
    return () => { cancelled = true }
  }, [supabase])
  }, [query, supabase])

  const inviteSelected = async () => {
    if (!selected) return
    setLoading(true); setStatus(null)
    try {
      const res = await fetch('/api/duo/invite', {
        method: 'POST', headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ duoPostId, invitee_user_id: selected.id, reserve: reserveExclusive })
      })
      if (!res.ok) throw new Error('Failed to send invite')
      setStatus(`Invite sent to ${selected.name}${reserveExclusive ? ' (reserved)' : ''}.`)
    } catch (e: any) {
      setStatus(e.message || 'Invite failed')
    } finally {
      setLoading(false)
    }
  }

  const shareNative = async () => {
    const title = 'Join my OnlyDuo on OnlyDiary'
    const text = 'Record Part B — it takes 15 seconds. Then we publish it together.'
    const url = respondUrl
    if (typeof navigator !== 'undefined' && (navigator as any).share) {
      try { await (navigator as any).share({ title, text, url }); return } catch {}
    }
    // Fallback: copy
    await navigator.clipboard.writeText(`${text} ${url}`)
    setStatus('Link copied to clipboard')
  }

  const copyLink = async () => {
    await navigator.clipboard.writeText(respondUrl)
    setStatus('Link copied to clipboard')
  }

  return (
    <div className="rounded-2xl border border-gray-200 bg-white/80 backdrop-blur-sm shadow-sm p-5 sm:p-6 mb-6">
      {/* Header */}
      <div className="mb-2">
        <h2 className="text-lg font-serif text-gray-900">Tag a Friend</h2>
        <p className="text-sm text-gray-600">Some stories take two. Record your part, tag a friend to reply, and decide if it goes public.</p>
      </div>


        {/* Quick picks: people you follow */}
        {(!query || query.length === 0) && quickPicks.length > 0 && (
          <div className="mt-3">
            <div className="text-xs text-gray-500 mb-2">People you follow</div>
            <div className="flex gap-2 overflow-x-auto pb-1 [-ms-overflow-style:none] [scrollbar-width:none]">
              <style jsx>{`div::-webkit-scrollbar { display: none; }`}</style>
              {quickPicks.map((u) => (
                <button
                  key={u.id}
                  onClick={() => { const s = { id: u.id, name: u.name as string }; setSelected(s); onFriendSelected?.(s) }}
                  className={`flex items-center gap-2 border rounded-full px-2.5 py-1.5 bg-white/70 hover:border-gray-300 ${selected?.id === u.id ? 'border-purple-300 ring-2 ring-purple-100' : 'border-gray-200'}`}
                >
                  <div className="w-6 h-6 rounded-full overflow-hidden bg-gray-200">
                    {u.profile_picture_url || u.avatar ? (
                      // eslint-disable-next-line @next/next/no-img-element
                      <img src={(u.profile_picture_url || u.avatar) as string} alt={u.name as string} className="w-full h-full object-cover" />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-[10px] text-gray-600">{(u.name as string)?.charAt(0).toUpperCase()}</div>
                    )}
                  </div>
                  <span className="text-xs text-gray-800 truncate max-w-[120px]">{u.name}</span>
                </button>
              ))}
            </div>
          </div>
        )}


      {/* Search */}
      <div className="mb-4">
        <div className="relative">
          <input
            value={query}
            onChange={e => setQuery(e.target.value)}
            placeholder="Search by name…"
            className="w-full border border-gray-300 rounded-xl px-3 py-2 pl-10 text-sm shadow-sm focus:ring-2 focus:ring-purple-200 focus:border-purple-300"
          />
          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">🔎</span>
        </div>
        {results.length > 0 && (
          <div className="mt-3 grid grid-cols-1 sm:grid-cols-2 gap-2">
            {results.map(u => {
              const name = u.name
              const q = query.trim()
              const idx = name.toLowerCase().indexOf(q.toLowerCase())
              const before = idx >= 0 ? name.slice(0, idx) : name
              const match = idx >= 0 ? name.slice(idx, idx + q.length) : ''
              const after = idx >= 0 ? name.slice(idx + q.length) : ''
              return (
                <button
                  key={u.id}
                  onClick={() => { const s = { id: u.id, name: u.name }; setSelected(s); onFriendSelected?.(s) }}
                  className={`group flex items-center gap-3 border rounded-xl p-2.5 text-left hover:border-gray-300 bg-white/60 ${selected?.id === u.id ? 'border-purple-300 ring-2 ring-purple-100' : 'border-gray-200'}`}
                >
                  <div className="w-9 h-9 rounded-full bg-gray-200 overflow-hidden flex items-center justify-center">
                    {u.profile_picture_url || u.avatar ? (
                      // eslint-disable-next-line @next/next/no-img-element
                      <img src={(u.profile_picture_url || u.avatar) as string} alt={u.name} className="w-full h-full object-cover" />
                    ) : (
                      <span className="text-sm text-gray-600">{u.name.charAt(0).toUpperCase()}</span>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-sm text-gray-800 truncate">
                      {idx >= 0 ? (
                        <>
                          <span>{before}</span><span className="bg-yellow-100">{match}</span><span>{after}</span>
                        </>
                      ) : (
                        <span>{name}</span>
                      )}
                    </div>
                  </div>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity text-xs text-purple-600">Select</div>
                </button>
              )
            })}
          </div>
        )}
        {selected && (
          <div className="mt-2 text-sm text-gray-700">Selected: <span className="font-medium">{selected.name}</span></div>
        )}
      </div>

      {/* Actions */}
      <div className="mt-3 flex flex-col sm:flex-row sm:items-center gap-2">
        <div className="flex items-center gap-2">
          <button onClick={shareNative} className="px-3 py-2 rounded-md bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 text-sm shadow">Invite Friends</button>
          <button onClick={copyLink} className="px-3 py-2 rounded-md border border-gray-300 text-sm bg-white">Copy Link</button>
        </div>
        <div className="flex-1" />
        <div className="flex items-center gap-2">
          <input id="reserve" type="checkbox" checked={reserveExclusive} onChange={e => setReserveExclusive(e.target.checked)} className="rounded" />
          <label htmlFor="reserve" className="text-sm text-gray-700">Exclusive to selected friend</label>
        </div>
        <button onClick={inviteSelected} disabled={!selected || loading} className="px-3 py-2 rounded-md bg-gray-900 text-white disabled:opacity-60 text-sm">
          {loading ? 'Sending…' : 'Invite Selected Friend'}
        </button>
      </div>

      {status && <div className="mt-2 text-xs text-gray-600">{status}</div>}
    </div>
  )
}

