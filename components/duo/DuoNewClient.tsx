"use client"

import { useState } from 'react'
import { DuoInvite } from '@/components/duo/DuoInvite'
import { DuoUploader } from '@/components/duo/DuoUploader'

export function DuoNewClient({ duoPostId, manageUrl }: { duoPostId: string; manageUrl: string }) {
  const [friend, setFriend] = useState<{ id: string; name: string } | null>(null)

  return (
    <div>
      {/* Step 1: Tag a Friend / Invite */}
      <DuoInvite duoPostId={duoPostId} onFriendSelected={setFriend} />

      {/* Step 2: Upload appears only after friend selected */}
      {friend && (
        <div className="rounded-xl border border-gray-200 bg-white p-4 sm:p-5">
          <h2 className="text-base font-medium text-gray-900 mb-2">Upload Part A</h2>
          <p className="text-sm text-gray-600 mb-3">For {friend.name} • MP4 only • Max 25MB</p>
          <DuoUploader duoPostId={duoPostId} partNumber={1} />
          <div className="mt-3 text-xs text-gray-600">After upload, you can manage at <a href={manageUrl} className="text-blue-600 hover:underline">{manageUrl}</a></div>
        </div>
      )}
    </div>
  )
}

