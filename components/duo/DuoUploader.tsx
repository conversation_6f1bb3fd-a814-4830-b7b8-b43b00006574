"use client"

import { useState } from 'react'

export function DuoUploader({
  duoPostId,
  partNumber,
  onDone,
}: {
  duoPostId: string
  partNumber: 1 | 2
  onDone?: () => void
}) {
  const [busy, setBusy] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleFile = async (file: File) => {
    setBusy(true); setError(null)
    try {
      // Basic constraint: max 25MB
      if (file.size > 25 * 1024 * 1024) throw new Error('Max file size is 25MB')

      const presignRes = await fetch('/api/duo/presign-upload', {
        method: 'POST', headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ duoPostId, partNumber, filename: file.name, sizeBytes: file.size, mimeType: file.type || 'video/mp4' })
      })
      if (!presignRes.ok) throw new Error('Failed to prepare upload')
      const { uploadUrl, r2Key } = await presignRes.json()

      // PUT to R2
      const putRes = await fetch(uploadUrl, { method: 'PUT', body: file, headers: { 'Content-Type': file.type || 'video/mp4' } })
      if (!putRes.ok) throw new Error('Upload failed')

      // Minimal media metadata (duration/size can be probed client-side later)
      const media = {
        type: 'video',
        src_r2_key: r2Key,
        hls_manifest: null,
      }

      const commitRes = await fetch('/api/duo/commitPart', {
        method: 'POST', headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ duoPostId, partNumber, media })
      })
      if (!commitRes.ok) throw new Error('Failed to save part')
      onDone?.()
    } catch (e: any) {
      setError(e.message || 'Upload failed')
    } finally {
      setBusy(false)
    }
  }

  return (
    <div className="mt-3">
      <label className="inline-flex items-center gap-2 text-sm text-blue-700 hover:text-blue-800 cursor-pointer">
        <span className="px-3 py-2 bg-blue-50 rounded border border-blue-200">{busy ? 'Uploading…' : `Upload Part ${partNumber}`}</span>
        <input type="file" accept="video/mp4, audio/mp4, audio/aac, audio/m4a" className="hidden" disabled={busy} onChange={e => {
          const f = e.target.files?.[0]; if (f) handleFile(f)
        }} />
      </label>
      {error && <div className="text-xs text-red-600 mt-1">{error}</div>}
    </div>
  )
}

