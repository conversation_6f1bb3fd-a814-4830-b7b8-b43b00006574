"use client"

import { useEffect, useRef, useState } from 'react'

type MediaVideo = {
  type: 'video'
  src_r2_key: string
  hls_manifest?: string | null
}

export function DuoVideoPlayer({
  partA,
  partB,
  signedPlayback = false,
}: {
  partA: MediaVideo
  partB?: MediaVideo
  signedPlayback?: boolean
}) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [muted, setMuted] = useState(true)
  const [playingB, setPlayingB] = useState(false)

  // Public fallback URLs
  const publicA = `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL || ''}/${partA.src_r2_key}`
  const publicB = partB ? `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL || ''}/${partB.src_r2_key}` : undefined

  // Signed URLs state
  const [signedA, setSignedA] = useState<string | null>(null)
  const [signedB, setSignedB] = useState<string | null>(null)

  // Fetch signed GET URLs (only when enabled)
  useEffect(() => {
    if (!signedPlayback) return
    let cancelled = false
    async function sign() {
      try {
        const resA = await fetch(`/api/media/sign?key=${encodeURIComponent(partA.src_r2_key)}`)
        if (resA.ok) {
          const j = await resA.json()
          if (!cancelled) setSignedA(j.url)
        }
        if (partB) {
          const resB = await fetch(`/api/media/sign?key=${encodeURIComponent(partB.src_r2_key)}`)
          if (resB.ok) {
            const j2 = await resB.json()
            if (!cancelled) setSignedB(j2.url)
          }
        }
      } catch {
        // ignore; fallback to public URLs
      }
    }
    sign()
    return () => { cancelled = true }
  }, [signedPlayback, partA.src_r2_key, partB?.src_r2_key])

  useEffect(() => {
    const el = videoRef.current
    if (!el) return

    const onEnded = () => {
      if (partB && !playingB) {
        setPlayingB(true)
        const nextSrc = signedB || publicB
        if (nextSrc) {
          el.src = nextSrc
          el.currentTime = 0
          el.play().catch(() => {})
        }
      }
    }

    el.addEventListener('ended', onEnded)
    return () => el.removeEventListener('ended', onEnded)
  }, [partB, playingB, signedB, publicB])

  const initialSrc = signedA || publicA

  return (
    <div className="relative w-full" style={{ aspectRatio: '9/16' }}>
      <video
        ref={videoRef}
        className="w-full h-full bg-black"
        src={initialSrc}
        playsInline
        muted={muted}
        controls
        preload="metadata"
      />
      <button
        className="absolute bottom-3 right-3 bg-black/60 text-white text-xs rounded px-2 py-1"
        onClick={() => setMuted(m => !m)}
      >{muted ? 'Unmute' : 'Mute'}</button>
      {partB && !playingB && (
        <div className="absolute top-2 right-2 text-xs bg-black/50 text-white px-2 py-1 rounded">
          Part 1/2
        </div>
      )}
      {partB && playingB && (
        <div className="absolute top-2 right-2 text-xs bg-black/50 text-white px-2 py-1 rounded">
          Part 2/2
        </div>
      )}
    </div>
  )
}
