"use client"

import { useState } from 'react'
import { DuoUploader } from './DuoUploader'

export function DuoRespondClient({
  duoPostId,
  initiatorName,
  canUpload,
  status,
}: {
  duoPostId: string
  initiatorName?: string
  canUpload: boolean
  status: 'pending' | 'awaiting_approval' | 'completed' | 'expired' | string
}) {
  const [done, setDone] = useState(false)

  if (status === 'completed') {
    return (
      <div className="max-w-xl mx-auto p-6">
        <h1 className="text-xl font-semibold mb-2">Duo Completed</h1>
        <p className="text-gray-600">This Duo has already been completed and published.</p>
      </div>
    )
  }

  if (status === 'awaiting_approval') {
    return (
      <div className="max-w-xl mx-auto p-6">
        <h1 className="text-xl font-semibold mb-2">Waiting for Approval</h1>
        <p className="text-gray-600">The initiator is reviewing the response. You will see it on the timeline once approved.</p>
      </div>
    )
  }

  return (
    <div className="max-w-xl mx-auto p-6">
      <h1 className="text-xl font-semibold mb-2">Respond to Duo</h1>
      {initiatorName && (
        <p className="text-gray-600 mb-4">Continue {initiatorName}'s Duo with your Part B.</p>
      )}

      {canUpload ? (
        <>
          {!done ? (
            <>
              <p className="text-sm text-gray-600 mb-3">Upload a short MP4 (≤ 25MB). After upload, the initiator must approve before it appears on the timeline.</p>
              <DuoUploader duoPostId={duoPostId} partNumber={2} onDone={() => setDone(true)} />
            </>
          ) : (
            <div className="rounded-lg bg-green-50 border border-green-200 p-4">
              <p className="text-green-800 font-medium">Response uploaded</p>
              <p className="text-green-700 text-sm">It has been sent for approval. You can close this page.</p>
            </div>
          )}
        </>
      ) : (
        <div className="rounded-lg bg-yellow-50 border border-yellow-200 p-4">
          <p className="text-yellow-800 font-medium">You are not the invited responder</p>
          <p className="text-yellow-700 text-sm">Only the invited user can upload Part B for this Duo.</p>
        </div>
      )}
    </div>
  )
}

