'use client'

import React, { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { AudioPost } from './AudioPost'
import { PaywallContent } from './PaywallContent'
import { Day1Badge } from './Day1Badge'
import { ReactionSystem } from './ReactionSystem'
import { CompactCommentsSection } from './CompactCommentsSection'
import { CompactBookCommentsSection } from './CompactBookCommentsSection'
import { CompactRecipeCommentsSection } from './CompactRecipeCommentsSection'
import { ShareButton } from './ShareButton'
import { FollowButton } from './FollowButton'
import { GoldenPenRating } from './GoldenPenRating'
import Image from 'next/image'
import Link from 'next/link'

const OD_PURPLE = '#A020F0'
const CARD_BG_V2 = 'bg-[#FAFAFA]'
const CARD_BORDER_V2 = 'border border-[#EDEDED]'
const CARD_SHADOW_V2 = 'shadow-[0_2px_6px_rgba(0,0,0,0.04)]'
const CARD_RADIUS_V2 = 'rounded-[14px]'
const USERNAME_COLOR_V2 = 'text-[#A020F0]'
const TIMESTAMP_COLOR_V2 = 'text-[#999]'
const BODY_TEXT_V2 = 'text-[#555]'
const TITLE_TEXT_V2 = 'text-[#222]'

function classNames(...classes: (string | false | null | undefined)[]) {
  return classes.filter(Boolean).join(' ')
}



function formatNumber(n?: number | null) {
  const v = typeof n === 'number' && n >= 0 ? n : 0
  if (v < 1000) return `${v}`
  if (v < 1_000_000) return `${(v / 1000).toFixed(v % 1000 === 0 ? 0 : 1)}k`
  return `${(v / 1_000_000).toFixed(v % 1_000_000 === 0 ? 0 : 1)}M`
}


interface DiaryEntry {
  id: string
  title: string
  body_md: string
  created_at: string
  is_free: boolean
  has_access: boolean
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
    badge_tier?: string
  }
  view_count: number
  reactions?: Record<string, number>
  userReaction?: string | null
  photos?: Array<{ id: string; url: string; alt_text: string }>
  videos?: Array<{ id: string; r2_public_url: string; title?: string; view_count?: number; custom_thumbnail_url?: string }>
  type: 'diary'
  isFollowing: boolean
  isSubscribed?: boolean
}

interface AudioPostType {
  id: string
  audio_url: string
  description?: string
  duration_seconds: number
  love_count: number
  reply_count: number
  play_count: number
  created_at: string
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
    badge_tier?: string
  }
  type: 'audio'
  isFollowing: boolean
  isSubscribed?: boolean
}

interface BookReleaseType {
  id: string
  title: string
  description: string
  cover_image_url?: string
  genre?: string
  price_amount: number
  average_rating?: number
  review_count?: number
  sales_count?: number
  created_at: string
  author_name?: string
  is_ebook: boolean
  slug?: string
  user_id: string
  reactions?: Record<string, number>
  userReaction?: string | null
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
    badge_tier?: string
  }
  type: 'book'
  isFollowing: boolean
  isSubscribed?: boolean
}

interface RecipePostType {
  id: string
  title: string
  description?: string
  created_at: string
  is_free: boolean
  price_cents?: number
  cooked_count?: number
  rank?: number | null
  cover_photo_url?: string
  recipe_photos?: Array<{ id: string; url: string; alt_text?: string; created_at?: string }>
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number

    badge_tier?: string
  }
  type: 'recipe'
  isFollowing: boolean
  isSubscribed?: boolean
}

type TimelinePost = DiaryEntry | AudioPostType | BookReleaseType | RecipePostType

interface UnifiedTimelineProps {
  currentUserId?: string
  onUserClick?: (userId: string) => void
  reloadKey?: number
  filterEnabled?: { diary: boolean; audio: boolean; book: boolean; recipe: boolean }
}

export function UnifiedTimeline({ currentUserId, onUserClick, reloadKey, filterEnabled }: UnifiedTimelineProps) {
  const [posts, setPosts] = useState<TimelinePost[]>([])
  const [openComments, setOpenComments] = useState<Record<string, boolean>>({})
  const [loading, setLoading] = useState(true)
  const [openReactionBreakdown, setOpenReactionBreakdown] = useState<Record<string, boolean>>({})
  const [hasMore, setHasMore] = useState(true)
  const [commentCounts, setCommentCounts] = useState<Record<string, number>>({})
  const [recipeCommentCounts, setRecipeCommentCounts] = useState<Record<string, number>>({})
  const [trackedImpressions, setTrackedImpressions] = useState<Set<string>>(new Set())
  const [experimentalStyle, setExperimentalStyle] = useState(false)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search)
      setExperimentalStyle(params.get('style') === 'v2')
    }
  }, [reloadKey])

  const supabase = createSupabaseClient()

  useEffect(() => {
    setPosts([])
    setHasMore(true)
    loadPosts(0)
  }, [reloadKey])
  // Refetch when filters change (client-side filtering fallback)
  useEffect(() => {
    if (!filterEnabled) return
    setPosts(prev => prev.filter(p => filterEnabled[p.type as 'diary'|'audio'|'book'|'recipe']))
  }, [filterEnabled])


  // Track impression when post comes into view
  const trackImpression = async (contentType: string, contentId: string) => {
    const key = `${contentType}-${contentId}`
    if (trackedImpressions.has(key)) return

    try {
      await fetch('/api/engagement/impression', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contentType,
          contentId,
          source: 'timeline'
        })
      })
      setTrackedImpressions(prev => new Set(prev).add(key))
    } catch (error) {
      console.error('Failed to track impression:', error)
    }
  }

  // Track click when user clicks on post
  const trackClick = async (contentType: string, contentId: string) => {
    try {
      await fetch('/api/engagement/click', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contentType,
          contentId,
          source: 'timeline'
        })
      })
    } catch (error) {
      console.error('Failed to track click:', error)
    }
  }

  // Intersection Observer for tracking impressions
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement
            const contentType = element.dataset.contentType
            const contentId = element.dataset.contentId

            if (contentType && contentId) {
              trackImpression(contentType, contentId)
            }
          }
        })
      },
      { threshold: 0.5 } // Track when 50% of post is visible
    )

    // Observe all post elements
    const postElements = document.querySelectorAll('[data-content-type]')
    postElements.forEach(el => observer.observe(el))

    return () => observer.disconnect()
  }, [posts, trackedImpressions])

  // Load comment counts for diary entries and books
  useEffect(() => {
    const diaryPosts = posts.filter(post => post.type === 'diary')
    const bookPosts = posts.filter(post => post.type === 'book')
    const recipePosts = posts.filter(post => post.type === 'recipe')

    if (diaryPosts.length > 0) {
      loadDiaryCommentCounts(diaryPosts.map(post => post.id))
    }
    if (bookPosts.length > 0) {
      loadBookCommentCounts(bookPosts.map(post => post.id))
    }
    if (recipePosts.length > 0) {
      loadRecipeCommentCounts(recipePosts.map(post => post.id))
    }
  }, [posts])

  const loadDiaryCommentCounts = async (entryIds: string[]) => {
    try {
      const { data, error } = await supabase
        .from('comments')
        .select('diary_entry_id')
        .in('diary_entry_id', entryIds)
        .eq('is_deleted', false)

      if (!error && data) {
        const counts: Record<string, number> = {}
        entryIds.forEach(id => counts[id] = 0)

        data.forEach(comment => {
          counts[comment.diary_entry_id] = (counts[comment.diary_entry_id] || 0) + 1
        })

        setCommentCounts(prev => ({ ...prev, ...counts }))
      }
    } catch (err) {
      console.error('Error loading diary comment counts:', err)
    }
  }

  const loadBookCommentCounts = async (bookIds: string[]) => {
    try {
      const { data, error } = await supabase
        .from('comments')
        .select('book_id')
        .in('book_id', bookIds)
        .eq('is_deleted', false)

      if (!error && data) {
        const counts: Record<string, number> = {}
        bookIds.forEach(id => counts[id] = 0)

        ;(data as any[]).forEach((comment) => {
          counts[comment.book_id] = (counts[comment.book_id] || 0) + 1
        })

        setCommentCounts(prev => ({ ...prev, ...counts }))
      }
    } catch (err) {
      console.error('Error loading book comment counts:', err)
    }
  }

  const loadRecipeCommentCounts = async (recipeIds: string[]) => {
    try {
      const { data, error } = await supabase
        .from('comments')
        .select('recipe_id')
        .in('recipe_id', recipeIds)
        .eq('is_deleted', false)

      if (!error && data) {
        const counts: Record<string, number> = {}
        recipeIds.forEach(id => counts[id] = 0)

        ;(data as any[]).forEach((comment) => {
          counts[comment.recipe_id] = (counts[comment.recipe_id] || 0) + 1
        })

        setRecipeCommentCounts(prev => ({ ...prev, ...counts }))
      }
    } catch (err) {
      console.error('Error loading recipe comment counts:', err)
    }
  }


  const toggleOpenComments = (id: string) =>
    setOpenComments(prev => ({ ...prev, [id]: !prev[id] }))

  const loadPosts = async (offset = 0) => {
    try {
      const response = await fetch(`/api/timeline?limit=20&offset=${offset}`)
      const data = await response.json()

      const newPosts: TimelinePost[] = data.posts || []

      // Apply client-side filter immediately for responsiveness
      const incoming = filterEnabled
        ? newPosts.filter(p => filterEnabled[p.type as 'diary'|'audio'|'book'|'recipe'])
        : newPosts

      if (offset === 0) {
        setPosts(incoming)
      } else {
        setPosts(prev => [...prev, ...incoming])
      }

      setHasMore(data.hasMore || false)
    } catch (error) {
      console.error('Error loading timeline:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAudioLove = async (postId: string) => {
    try {
      const response = await fetch(`/api/audio/posts/${postId}/love`, {
        method: 'POST'
      })

      if (response.ok) {
        const { loved } = await response.json()

        setPosts(prev => prev.map(post => {
          if (post.type === 'audio' && post.id === postId) {
            return {
              ...post,
              love_count: loved ? post.love_count + 1 : post.love_count - 1
            }
          }
          return post
        }))
      }
    } catch (error) {
      console.error('Error toggling audio love:', error)
    }
  }

  const handleAudioReply = (postId: string) => {
    // TODO: Implement audio reply functionality
    console.log('Audio reply for post:', postId)
  }

  const handleReactionUpdate = (
    contentId: string,
    contentType: 'diary' | 'audio' | 'book' | 'recipe',
    reactions: Record<string, number>,
    userReaction: string | null
  ) => {
    setPosts(prev => prev.map(post => {
      if (post.id === contentId && post.type === contentType) {
        if (contentType === 'book') {
          return { ...post, reactions, userReaction } as BookReleaseType
        } else if (contentType === 'diary') {
          return { ...post, reactions, userReaction } as DiaryEntry
        } else if (contentType === 'recipe') {
          return { ...post, reactions, userReaction } as any
        } else if (contentType === 'audio') {
          return { ...post, reactions, userReaction } as any
        }
        return post
      }
      return post
    }))
  }



  const formatTimeAgo = (dateString: string) => {
    const now = new Date()
    const postDate = new Date(dateString)
    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'now'
    if (diffInMinutes < 60) return `${diffInMinutes}m`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`
    return `${Math.floor(diffInMinutes / 1440)}d`
  }

  if (loading) {
    return (
      <div className="space-y-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 animate-pulse">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-32 mb-2"></div>
                <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-20"></div>
              </div>
            </div>
            <div className="h-20 bg-gradient-to-r from-gray-200 to-gray-300 rounded mb-4"></div>
            <div className="flex gap-4">
              <div className="h-8 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-16"></div>
              <div className="h-8 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-16"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-0 w-full overflow-x-hidden">
      {posts.map((post) => {
        if (post.type === 'audio') {
          return (
            <div
              key={`audio-${post.id}`}
              data-content-type="audio"
              data-content-id={post.id}
            >
              <AudioPost
                post={post}
                currentUserId={currentUserId}
                isFollowing={post.isFollowing}
                onLove={handleAudioLove}
                onReply={handleAudioReply}
                onUserClick={onUserClick}
              />
            </div>
          );
        } else if (post.type === 'book') {
          // Book release
          return (
            <div
              key={`book-${post.id}`}
              className={classNames(
                experimentalStyle ? `${CARD_BG_V2} ${CARD_BORDER_V2} ${CARD_SHADOW_V2} ${CARD_RADIUS_V2}` : 'bg-white rounded-xl shadow-sm border border-gray-100',
                'p-4 hover:shadow-lg hover:border-blue-200 transition-all duration-300 hover:bg-gradient-to-br hover:from-white hover:to-blue-50/20 w-full overflow-hidden relative'
              )}
              data-content-type="book"
              data-content-id={post.id}
            >
              {experimentalStyle && <div className="absolute top-0 left-0 right-0 h-[3px]" style={{ backgroundColor: OD_PURPLE }} />}
              {/* Header */}
              <div className="flex items-start gap-3 mb-3 relative">
                <button
                  onClick={() => onUserClick?.(post.user.id)}
                  className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0 hover:ring-2 hover:ring-gray-300 transition-all cursor-pointer"
                >
                  {post.user.avatar || post.user.profile_picture_url ? (
                    <Image
                      src={(post.user.avatar || post.user.profile_picture_url) as string}
                      alt={post.user.name || 'User avatar'}
                      width={48}
                      height={48}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <span className="text-gray-500 text-lg">👤</span>
                  )}
                </button>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => onUserClick?.(post.user.id)}
                        className="font-semibold text-gray-900 hover:text-blue-600 transition-colors truncate"
                      >
                        {post.user.name}
                      </button>
                      {post.user.has_day1_badge && (
                        <Day1Badge
                          signupNumber={post.user.signup_number}
                          badgeTier={post.user.badge_tier}
                          size="sm"
                          className="flex-shrink-0"
                        />
                      )}
                      {experimentalStyle && (
                        <div className="absolute right-2 top-2">
                          {!post.is_free && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-white text-[10px] font-medium" style={{ background: 'linear-gradient(90deg,#A020F0,#FF4AA5)' }}>
                              Premium
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                    <span className="text-gray-400 text-sm">•</span>
                    <span className="text-gray-500 text-sm">
                      {formatTimeAgo(post.created_at)}
                    </span>
                  </div>

                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span className="text-blue-500">📚</span>
                    <span>Published a Book</span>
                    {post.isSubscribed && (
                      <div className="flex items-center gap-1 bg-purple-50 px-2 py-0.5 rounded-full">
                        <span className="w-1.5 h-1.5 bg-purple-500 rounded-full animate-pulse"></span>
                        <span className="text-purple-700 uppercase tracking-wide text-[10px] font-medium">SUBSCRIBER</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Follow Button - Top Right */}
                {currentUserId && currentUserId !== post.user.id && (
                  <div className="absolute top-0 right-0">
                    <FollowButton
                      writerId={post.user.id}
                      writerName={post.user.name}
                      initialIsFollowing={post.isFollowing}
                      size="sm"
                    />
                  </div>
                )}
              </div>

              {/* Book Content */}
              <Link
                href={`/books/${post.slug || post.id}`}
                onClick={() => trackClick('book', post.id)}
                className="block"
              >
                <div className="flex gap-3 sm:gap-4 w-full overflow-hidden">
                  {/* Book Cover */}
                  <div className="w-16 sm:w-20 rounded flex-shrink-0 overflow-hidden">
                    {post.cover_image_url ? (
                      <Image
                        src={post.cover_image_url}
                        alt={post.title}
                        width={80}
                        height={120}
                        className="w-full h-auto object-contain"
                      />
                    ) : (
                      <div className="w-full aspect-[3/4] flex items-center justify-center">
                        <span className="text-2xl opacity-50">📖</span>
                      </div>
                    )}
                  </div>

                  {/* Book Details */}
                  <div className="flex-1 min-w-0 overflow-hidden">
                    <h2 className={classNames('text-base sm:text-lg font-serif mb-1 leading-tight line-clamp-2 break-words', experimentalStyle ? TITLE_TEXT_V2 : 'text-gray-900')}>
                      {post.title}
                    </h2>

                    {/* Reviews under title */}
                    {post.average_rating && post.average_rating > 0 && post.review_count && post.review_count > 0 && (
                      <div className="mb-2">
                        <GoldenPenRating
                          rating={post.average_rating}
                          totalReviews={post.review_count}
                          size="sm"
                          showText={true}
                          compact
                        />
                      </div>
                    )}

                    <p className="text-gray-600 text-xs sm:text-sm line-clamp-3 mb-3 break-words">
                      {post.description}
                    </p>

                    {/* Book Metadata */}
                    <div className="flex items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-500 flex-wrap">
                      {post.genre && (
                        <span className="bg-gray-100 px-2 py-1 rounded text-xs">
                          {post.genre}
                        </span>
                      )}
                      <span className="font-medium text-green-600">
                        {post.price_amount === 0 ? 'Free' : `$${(post.price_amount / 100).toFixed(2)}`}
                      </span>
                    </div>
                  </div>
                </div>
              </Link>

              {/* Summary row + action bar (FB style) */}
              <div className="mt-3">
                <div className="flex items-center justify-between text-xs text-gray-600">
                  <div className="flex items-center gap-1">
                    <span>💬</span>
                    <span>{formatNumber(commentCounts[post.id] || 0)}</span>
                  </div>
                  {typeof post.rank === 'number' && (
                    <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-[10px] font-semibold bg-yellow-100 text-yellow-800 border border-yellow-200">
                      {post.rank === 1 ? '#1 Bestseller' : `#${post.rank} in Bestsellers`}
                    </span>
                  )}
                </div>
                <div className="flex items-center justify-between border-t border-gray-100 mt-2 pt-2">
                  <div className="flex items-center gap-2">
                    <ReactionSystem
                      variant="fb"
                      contentId={post.id}
                      contentType="book"
                      currentUserId={currentUserId}
                      initialReactions={post.reactions || {}}
                      userReaction={post.userReaction}
                      onReactionUpdate={(reactions, userReaction) =>
                        handleReactionUpdate(post.id, 'book', reactions, userReaction)
                      }
                    />
                    <button
                      onClick={() => toggleOpenComments(post.id)}
                      className="flex items-center gap-2 text-gray-600 hover:text-blue-600 text-sm"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 8h10M7 12h6m-6 4h8"/></svg>
                      <span>Comment</span>
                    </button>
                  </div>
                  <ShareButton
                    title={post.title}
                    writerName={post.user.name}
                    contentType="book"
                    contentId={post.id}
                    variant="compact"
                    url={`${typeof window !== 'undefined' ? window.location.origin : ''}/books/${post.slug || post.id}`}
                  />
                </div>
              </div>
              {/* Inline comments for book */}
              <CompactBookCommentsSection
                bookId={post.id}
                canComment={!!currentUserId}
                userId={currentUserId}
                isOpen={!!openComments[post.id]}
                onToggle={() => toggleOpenComments(post.id)}
                commentCount={commentCounts[post.id] || 0}
              />


            </div>
          )
        } else if (post.type === 'recipe') {
          // Recipe post
          return (
            <div
              key={`recipe-${post.id}`}
              className={classNames(
                experimentalStyle ? `${CARD_BG_V2} ${CARD_BORDER_V2} ${CARD_SHADOW_V2} ${CARD_RADIUS_V2}` : 'bg-white rounded-xl shadow-sm border border-gray-100',
                'p-4 hover:shadow-lg hover:border-green-200 transition-all duration-300 w-full overflow-hidden relative'
              )}
              data-content-type="recipe"
              data-content-id={post.id}
            >
              {experimentalStyle && <div className="absolute top-0 left-0 right-0 h-[3px]" style={{ backgroundColor: OD_PURPLE }} />}
              {/* Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => onUserClick?.(post.user.id)}
                    className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0 hover:ring-2 hover:ring-gray-300 transition-all cursor-pointer"
                  >
                    {post.user.avatar || post.user.profile_picture_url ? (
                      <Image
                        src={(post.user.avatar || post.user.profile_picture_url) as string}
                        alt={post.user.name || 'User avatar'}
                        width={32}
                        height={32}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-sm text-gray-500">{post.user.name?.charAt(0).toUpperCase()}</span>
                    )}
                  </button>
                  <div>
                    <button
                      onClick={() => onUserClick?.(post.user.id)}
                      className="text-sm font-medium text-gray-900 hover:text-blue-600 transition-colors"
                    >
                      {post.user.name}
                    </button>
                    {post.user.has_day1_badge && (
                      <Day1Badge
                        signupNumber={post.user.signup_number}
                        badgeTier={post.user.badge_tier}
                        size="sm"
                        className="ml-1"
                      />
                    )}
                    {typeof post.rank === 'number' && (
                      <span className="ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-[10px] font-semibold bg-yellow-100 text-yellow-800 border border-yellow-200">{post.rank === 1 ? '#1 Recipe' : `#${post.rank} in Recipes`}</span>
                    )}
                    <p className="text-xs text-gray-500">{formatTimeAgo(post.created_at)}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {currentUserId && currentUserId !== post.user.id && (
                    <FollowButton
                      writerId={post.user.id}
                      writerName={post.user.name}
                      initialIsFollowing={post.isFollowing}
                      size="sm"
                    />
                  )}
                  <span className="text-green-600 text-sm">🍳 Recipe</span>
                  {!post.is_free && (
                    <span className={classNames('text-xs font-medium', experimentalStyle ? 'inline-flex items-center px-2 py-0.5 rounded-full text-white' : 'text-purple-600')} style={experimentalStyle ? { background: 'linear-gradient(90deg,#A020F0,#FF4AA5)' } : undefined}>
                      Premium
                    </span>
                  )}
                </div>
              </div>

              <Link href={`/recipes/${post.id}`} onClick={() => trackClick('recipe', post.id)} className="block">
                <div className="flex gap-3 sm:gap-4 w-full overflow-hidden">
                  <div className="w-16 sm:w-20 rounded flex-shrink-0 overflow-hidden">
                    {(() => {
                      const img = post.cover_photo_url || (post.recipe_photos && post.recipe_photos[0]?.url) || null
                      return img ? (
                        <Image
                          src={img}
                          alt={post.title}
                          width={80}
                          height={80}
                          className="w-full h-20 object-cover rounded"
                        />
                      ) : (
                        <div className="w-full h-20 bg-gray-100 rounded flex items-center justify-center">
                          <span className="text-2xl">🍳</span>
                        </div>
                      )
                    })()}
                  </div>

                  <div className="flex-1 min-w-0 overflow-hidden">
                    <h2 className={classNames('text-base sm:text-lg font-serif mb-1 leading-tight line-clamp-2 break-words', experimentalStyle ? TITLE_TEXT_V2 : 'text-gray-900')}>
                      {post.title}
                    </h2>
                    {post.description && (
                      <p className="text-gray-600 text-xs sm:text-sm line-clamp-2 mb-1 break-words">
                        {post.description}
                      </p>
                    )}
                    <div className="flex items-center gap-2 text-[11px] sm:text-xs text-gray-600 flex-wrap">
                      {post.cooked_count ? (
                        <span className="inline-flex items-center gap-1 bg-gray-50 border border-gray-200 rounded-full px-2 py-0.5">🍳 {post.cooked_count} cooked</span>
                      ) : null}
                      {post.price_cents !== undefined && post.price_cents !== null ? (
                        <span className="inline-flex items-center gap-1 text-green-700 font-medium">
                          {post.price_cents === 0 ? 'Free' : `$${(post.price_cents / 100).toFixed(2)}`}
                        </span>
                      ) : null}

                      {!post.is_free && (
                        <span className={classNames('font-medium', experimentalStyle ? 'inline-flex items-center px-2 py-0.5 rounded-full text-white text-xs' : 'text-purple-600')} style={experimentalStyle ? { background: 'linear-gradient(90deg,#A020F0,#FF4AA5)' } : undefined}>
                          Premium
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </Link>

              {/* Actions (match books/diary layout) */}
              <div className="px-4 pb-3 border-t border-gray-50">
                <div className="flex items-center justify-between pt-3">
                  <div className="flex items-center gap-4 text-gray-600">
                    {/* Summary row like FB: total reactions left */}
                    <button
                      className="flex items-center gap-1 text-xs hover:text-blue-600"
                      onClick={() => toggleOpenComments(post.id)}
                      title="View comments"
                    >
                      <span>💬</span>
                      <span>{formatNumber(recipeCommentCounts[post.id] || 0)}</span>
                    </button>
                  </div>
                </div>
                {/* Action bar: Like, Comment, Share */}
                <div className="flex items-center justify-between border-t border-gray-100 mt-2 pt-2">
                  <div className="flex items-center gap-2">
                    <ReactionSystem
                      variant="fb"
                      contentId={post.id}
                      contentType="recipe"
                      currentUserId={currentUserId}
                      initialReactions={post.reactions || {}}
                      userReaction={post.userReaction}
                      onReactionUpdate={(reactions, userReaction) =>
                        handleReactionUpdate(post.id, 'recipe', reactions, userReaction)
                      }
                    />
                    <button
                      onClick={() => toggleOpenComments(post.id)}
                      className="flex items-center gap-2 text-gray-600 hover:text-blue-600 text-sm"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 8h10M7 12h6m-6 4h8"/></svg>
                      <span>Comment</span>
                    </button>
                  </div>
                  <ShareButton
                    title={post.title}
                    writerName={post.user.name}
                    contentType="recipe"
                    contentId={post.id}
                    variant="compact"
                    url={`${typeof window !== 'undefined' ? window.location.origin : ''}/recipes/${post.id}`}
                  />
                </div>
              </div>
              {/* Inline comments for recipe */}
              <CompactRecipeCommentsSection
                recipeId={post.id}
                canComment={!!currentUserId}
                userId={currentUserId}
                isOpen={!!openComments[post.id]}
                onToggle={() => toggleOpenComments(post.id)}
                commentCount={recipeCommentCounts[post.id] || 0}
              />
            </div>
          )
        } else if (post.type === 'diary') {
          // Diary entry


          return (
            <div
              key={`diary-${post.id}`}
              className={classNames(
                experimentalStyle ? `${CARD_BG_V2} ${CARD_BORDER_V2} ${CARD_SHADOW_V2} ${CARD_RADIUS_V2}` : 'bg-white border-b border-gray-100',
                'hover:bg-gray-50 transition-colors relative'
              )}
              data-content-type="diary"
              data-content-id={post.id}
            >
              {experimentalStyle && <div className="absolute top-0 left-0 right-0 h-[3px]" style={{ backgroundColor: OD_PURPLE }} />}
              <Link
                href={`/d/${post.id}`}
                onClick={() => trackClick('diary', post.id)}
                className="block p-4"
              >
                {/* Clean mobile header */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <button
                      onClick={(e) => {
                        e.preventDefault()
                        onUserClick?.(post.user.id)
                      }}
                      className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0"
                    >
                      {(() => {
                        const avatarSrc = post.user.avatar || post.user.profile_picture_url
                        const isValidUrl = avatarSrc && (avatarSrc.startsWith('http') || avatarSrc.startsWith('/'))

                        if (isValidUrl) {
                          return (
                            <Image
                              src={avatarSrc as string}
                              alt={post.user.name || 'User avatar'}
                              width={32}
                              height={32}
                              className="w-full h-full object-cover"
                            />
                          )
                        } else if (avatarSrc && !isValidUrl) {
                          return <span className="text-sm">{avatarSrc}</span>
                        } else {
                          return (
                            <span className="text-sm text-gray-500">
                              {post.user.name?.charAt(0).toUpperCase()}
                            </span>
                          )
                        }
                      })()}
                    </button>
                    <div>
                      <button
                        onClick={(e) => {
                          e.preventDefault()
                          onUserClick?.(post.user.id)
                        }}
                        className="text-sm font-medium text-gray-900 hover:text-blue-600 transition-colors"
                      >
                        {post.user.name}
                      </button>
                      {post.user.has_day1_badge && (
                        <Day1Badge
                          signupNumber={post.user.signup_number}
                          badgeTier={post.user.badge_tier}
                          size="sm"
                          className="ml-1"
                        />
                      )}
                      <p className="text-xs text-gray-500">{formatTimeAgo(post.created_at)}</p>
                    </div>
                  </div>
                  {currentUserId && currentUserId !== post.user.id && (
                    <FollowButton
                      writerId={post.user.id}
                      writerName={post.user.name}
                      initialIsFollowing={post.isFollowing}
                      size="sm"
                    />
                  )}
                </div>

                {/* Title */}
                <h2 className="text-lg font-medium text-gray-900 mb-2 line-clamp-2 leading-tight">
                  {post.title}
                </h2>

                {/* Preview */}
                <p className="text-gray-600 line-clamp-3 leading-relaxed">
                  {post.body_md
                    .replace(/[#*`_~]/g, '')
                    .replace(/\n/g, ' ')
                    .substring(0, 180)
                    .trim()}
                </p>
                {post.body_md.length > 180 && (
                  <div className="mt-2">
                    <span className="text-xs font-medium bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                      Read more →
                    </span>
                  </div>
                )}

                {/* Media */}
                {post.photos && post.photos.length > 0 && (
                  <div className="mb-3 -mx-4">
                    <Image
                      src={post.photos[0].url}
                      alt={post.photos[0].alt_text || post.title}
                      width={400}
                      height={240}
                      className="w-full h-48 object-cover"
                    />
                  </div>
                )}

                {/* Meta */}
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>{post.body_md.split(' ').length} words</span>
                  {!post.is_free && (
                    <span className="text-purple-600 font-medium">Premium</span>
                  )}
                </div>
              </Link>

              {/* Actions */}
              <div className="px-4 pb-3 border-t border-gray-50">
                <div className="flex items-center justify-between pt-3">
                {/* Totals row: Reactions total and Comments total */}
                <div className="flex items-center justify-between text-xs text-gray-600">
                  <button
                    className="flex items-center gap-1 hover:text-blue-600"
                    onClick={() => setOpenReactionBreakdown(prev => ({ ...prev, [post.id]: !prev[post.id] }))}
                    title="View reactions breakdown"
                  >
                    <span>❤️</span>
                    <span>{Object.values(post.reactions || {}).reduce((a,b)=>a+b,0)}</span>
                  </button>
                  <button
                    className="flex items-center gap-1 hover:text-blue-600"
                    onClick={() => toggleOpenComments(post.id)}
                    title="View comments"
                  >
                    <span>💬</span>
                    <span>{formatNumber(commentCounts[post.id] || 0)}</span>
                  </button>
                </div>
                {openReactionBreakdown[post.id] && (
                  <div className="mt-1 text-[11px] text-gray-600 flex flex-wrap gap-2">
                    {Object.entries(post.reactions || {}).map(([type, count]) => (
                      <span key={type} className="inline-flex items-center gap-1 bg-gray-50 rounded px-2 py-0.5 border border-gray-200">
                        <span>{type}</span>
                        <span className="font-medium">{count}</span>
                      </span>
                    ))}
                    {(!post.reactions || Object.keys(post.reactions).length === 0) && (
                      <span className="text-gray-400">No reactions yet</span>
                    )}
                  </div>
                )}

                  {/* Action bar: React, Comment, Share */}
                  <div className="flex items-center justify-between border-t border-gray-100 mt-2 pt-2">
                  <div className="flex items-center gap-2">
                    <ReactionSystem
                      variant="fb"
                      contentId={post.id}
                      contentType="diary"
                      currentUserId={currentUserId}
                      initialReactions={post.reactions || {}}
                      userReaction={post.userReaction}
                      onReactionUpdate={(reactions, userReaction) =>
                        handleReactionUpdate(post.id, 'diary', reactions, userReaction)
                      }
                    />
                    <button
                      onClick={() => toggleOpenComments(post.id)}
                      className="flex items-center gap-2 text-gray-600 hover:text-blue-600 text-sm"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 8h10M7 12h6m-6 4h8"/></svg>
                      <span>Comment</span>
                    </button>
                  </div>
                  <ShareButton
                    title={post.title}
                    writerName={post.user.name}
                    contentType="diary"
                    contentId={post.id}
                    variant="compact"
                    url={`${typeof window !== 'undefined' ? window.location.origin : ''}/d/${post.id}`}
                  />
                </div>
                {/* Inline comments for diary */}
                <CompactCommentsSection
                  entryId={post.id}
                  canComment={!!currentUserId}
                  userId={currentUserId}
                  isOpen={!!openComments[post.id]}
                  onToggle={() => toggleOpenComments(post.id)}
                  commentCount={commentCounts[post.id] || 0}
                />
              </div>


            </div>



          )

        } else {
          // Return null for unknown post types
          return null;
        }
      })}

      {hasMore && (
        <div className="text-center py-8">
          <button
            onClick={() => loadPosts(posts.length)}
            className="px-6 py-3 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium text-gray-700 transition-colors"
          >
            Load More
          </button>
        </div>
      )}

      {posts.length === 0 && !loading && (
        <div className="text-center py-12">
          <p className="text-gray-500 font-serif text-lg mb-4">
            No posts yet. Start creating content!
          </p>
          <Link
            href="/write"
            className="inline-block px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
          >
            Create Your First Post
          </Link>
        </div>
      )}
    </div>
  );
}


