import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Get current user
    const { data: { user } } = await supabase.auth.getUser()

    // Load user realm preferences (defaults if none)
    let enabledRealms: Record<string, boolean> = { diary: true, audio: true, book: true, recipe: true }
    try {
      const { data: prefs } = await supabase.rpc('get_timeline_prefs_for_user')
      if (prefs && prefs.enabled) {
        enabledRealms = prefs.enabled as any
      }
    } catch (e) {
      console.warn('Timeline: prefs lookup failed, using defaults')
    }

    // Fetch diary entries
    const { data: diaryEntries, error: diaryError } = await supabase
      .from('diary_entries')
      .select(`
        id,
        title,
        body_md,
        created_at,
        is_free,
        love_count,
        view_count,
        user_id,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url,
          has_day1_badge,
          signup_number
        ),
        photos (
          id,
          url,
          alt_text
        ),
        videos (
          id,
          r2_public_url,
          custom_thumbnail_url,
          view_count
        )
      `)
      .eq('is_hidden', false)
      .order('created_at', { ascending: false })
      .limit(50) // Increased for early stage discovery

    if (diaryError) {
      console.error('Error fetching diary entries:', diaryError)
    }

    // Fetch audio posts
    const { data: audioPosts, error: audioError } = await supabase
      .from('audio_posts')
      .select(`
        id,
        audio_url,
        description,
        duration_seconds,
        love_count,
        reply_count,
        play_count,
        created_at,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url,
          has_day1_badge,
          signup_number
        )
      `)
      .order('created_at', { ascending: false })
      .limit(40) // Increased for early stage discovery

    if (audioError) {
      console.error('Error fetching audio posts:', audioError)
    }

    // Fetch recipes
    const { data: recipes, error: recipesError } = await supabase
      .from('recipes' as any)
      .select(`
        id,
        title,
        description,
        created_at,
        is_free,
        price_cents,
        cooked_count,
        cover_photo_url,
        user_id,
        users!inner (
          id,
          name,
          avatar,
          profile_picture_url,
          has_day1_badge,
          signup_number,
          badge_tier
        ),
        recipe_photos (
          id,
          url,
          alt_text,
          created_at
        )
      `)
      .eq('is_hidden', false)
      .order('created_at', { ascending: false })
      .order('created_at', { ascending: true, foreignTable: 'recipe_photos' })
      .limit(40)

    if (recipesError) {
      console.error('Error fetching recipes:', recipesError)
    }

    // Fetch book releases (completed books only)
    const { data: bookReleases, error: bookError } = await supabase
      .from('projects')
      .select(`
        id,
        title,
        description,
        cover_image_url,
        genre,
        price_amount,
        average_rating,
        review_count,
        sales_count,
        created_at,
        author_name,
        is_ebook,
        slug,
        user_id,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url,
          has_day1_badge,
          signup_number
        )
      `)
      .eq('is_ebook', true)
      .eq('is_complete', true)
      .eq('is_private', false)
      .order('created_at', { ascending: false })
      .limit(30) // Increased for early stage discovery

    if (bookError) {
      console.error('Error fetching book releases:', bookError)
    }

    // Get book reactions if we have books
    let bookReactions: Record<string, Record<string, number>> = {}
    let userBookReactions: Record<string, string> = {}

    if (bookReleases && bookReleases.length > 0) {
      const bookIds = bookReleases.map(book => book.id)

      // Get all reactions for these books
      const { data: reactions } = await supabase
        .from('reactions')
        .select('book_id, reaction_type, user_id')
        .in('book_id', bookIds)

      // Group reactions by book and type
      reactions?.forEach(reaction => {
        if (!bookReactions[reaction.book_id]) {
          bookReactions[reaction.book_id] = {}
        }
        bookReactions[reaction.book_id][reaction.reaction_type] =
          (bookReactions[reaction.book_id][reaction.reaction_type] || 0) + 1

        // Track current user's reactions
        if (user && reaction.user_id === user.id) {
          userBookReactions[reaction.book_id] = reaction.reaction_type
        }
      })
    }

    // Get diary entry reactions
    let diaryReactions: Record<string, Record<string, number>> = {}
    let userDiaryReactions: Record<string, string> = {}

    if (diaryEntries && diaryEntries.length > 0) {
      const diaryIds = diaryEntries.map(entry => entry.id)

      const { data: reactions } = await supabase
        .from('reactions')
        .select('diary_entry_id, reaction_type, user_id')
        .in('diary_entry_id', diaryIds)

      reactions?.forEach(reaction => {
        if (!diaryReactions[reaction.diary_entry_id]) {
          diaryReactions[reaction.diary_entry_id] = {}
        }
        diaryReactions[reaction.diary_entry_id][reaction.reaction_type] =
          (diaryReactions[reaction.diary_entry_id][reaction.reaction_type] || 0) + 1

        if (user && reaction.user_id === user.id) {
          userDiaryReactions[reaction.diary_entry_id] = reaction.reaction_type
        }
      })
    }

    // Get audio post reactions
    let audioReactions: Record<string, Record<string, number>> = {}
    let userAudioReactions: Record<string, string> = {}

    if (audioPosts && audioPosts.length > 0) {
      const audioIds = audioPosts.map(post => post.id)

      const { data: reactions } = await supabase
        .from('reactions')
        .select('audio_post_id, reaction_type, user_id')
        .in('audio_post_id', audioIds)

      reactions?.forEach(reaction => {
        if (!audioReactions[reaction.audio_post_id]) {
          audioReactions[reaction.audio_post_id] = {}
        }
        audioReactions[reaction.audio_post_id][reaction.reaction_type] =
          (audioReactions[reaction.audio_post_id][reaction.reaction_type] || 0) + 1

        if (user && reaction.user_id === user.id) {
          userAudioReactions[reaction.audio_post_id] = reaction.reaction_type
        }
      })
    }

    // Get follow and subscription status if user is logged in
    let followingIds: Set<string> = new Set()
    let subscribedIds: Set<string> = new Set()

    if (user) {
      // Get follows
      const { data: follows } = await supabase
        .from('follows')
        .select('writer_id')
        .eq('follower_id', user.id)

      followingIds = new Set(follows?.map(f => f.writer_id) || [])

      // Get subscriptions
      const { data: subscriptions } = await supabase
        .from('subscriptions')
        .select('writer_id')
        .eq('reader_id', user.id)
        .eq('status', 'active')

      subscribedIds = new Set(subscriptions?.map(s => s.writer_id) || [])
    }

    // EARLY STAGE: Show all content to everyone for better discovery
    // TODO: Re-enable follow-based filtering when user base grows

    // Get Code Book user ID to filter it out from general content
    const { data: codeBookUser } = await supabase
      .from('users')
      .select('id')
      .eq('name', 'OnlyDiary ChangeLog')
      .single()

    const codeBookUserId = codeBookUser?.id

    // Show all content (excluding Code Book) to promote discovery during early stage
    let filteredBookReleases = (bookReleases || [])
      .filter(book => book.user_id !== codeBookUserId)
      .slice(0, 15) // Increased limit for better variety

    let filteredDiaryEntries = (diaryEntries || [])
      .filter(entry => entry.user_id !== codeBookUserId)
      .slice(0, 25) // Increased limit for better variety

    let filteredAudioPosts = (audioPosts || [])
      .slice(0, 15) // Increased limit for better variety

    let filteredRecipes = (recipes || [])
      .filter(r => (r as any).users?.id !== codeBookUserId)
      .slice(0, 20)

    // Original follow-based filtering (commented out for early stage)
    /*
    if (user && (followingIds.size > 0 || subscribedIds.size > 0)) {
      // User has follows/subscriptions - show content from followed creators only
      filteredBookReleases = (bookReleases || []).filter(book =>
        subscribedIds.has(book.user_id) || followingIds.has(book.user_id)
      )
      filteredDiaryEntries = (diaryEntries || []).filter(entry =>
        subscribedIds.has(entry.user_id) || followingIds.has(entry.user_id)
      )
      filteredAudioPosts = (audioPosts || []).filter(post =>
        subscribedIds.has(post.user_id) || followingIds.has(post.user_id)
      )
    } else if (user) {
      // User has no follows - show recent general content
      filteredBookReleases = (bookReleases || []).filter(book => book.user_id !== codeBookUserId).slice(0, 10)
      filteredDiaryEntries = (diaryEntries || []).filter(entry => entry.user_id !== codeBookUserId).slice(0, 15)
      filteredAudioPosts = (audioPosts || []).slice(0, 10)
    } else {
      // Non-authenticated users get limited general content
      filteredBookReleases = (bookReleases || []).filter(book => book.user_id !== codeBookUserId).slice(0, 5)
      filteredDiaryEntries = (diaryEntries || []).filter(entry => entry.user_id !== codeBookUserId).slice(0, 10)
      filteredAudioPosts = (audioPosts || []).slice(0, 5)
    }
    */

    // Show all content (excluding Code Book) to promote discovery during early stage
    filteredRecipes = (recipes || [])
      .filter(r => r.user_id !== codeBookUserId)
      .slice(0, 20)

    // Apply realm preferences (skip disabled types)
    if (!enabledRealms.diary) filteredDiaryEntries = []
    if (!enabledRealms.audio) filteredAudioPosts = []
    if (!enabledRealms.book) filteredBookReleases = []
    if (!enabledRealms.recipe) filteredRecipes = []

    // Combine and format posts
    let combinedPosts = [
      ...filteredDiaryEntries.map(entry => ({
        ...entry,
        type: 'diary' as const,
        isFollowing: user ? followingIds.has(entry.user.id) : false,
        isSubscribed: user ? subscribedIds.has(entry.user.id) : false,
        has_access: entry.is_free || (user ? subscribedIds.has(entry.user.id) : false) || (user && entry.user.id === user.id),
        reactions: diaryReactions[entry.id] || {},
        userReaction: userDiaryReactions[entry.id] || null
      })),
      ...filteredAudioPosts.map(post => ({
        ...post,
        type: 'audio' as const,
        isFollowing: user ? followingIds.has(post.user.id) : false,
        isSubscribed: user ? subscribedIds.has(post.user.id) : false,
        reactions: audioReactions[post.id] || {},
        userReaction: userAudioReactions[post.id] || null
      })),
      ...filteredBookReleases.map(book => ({
        ...book,
        type: 'book' as const,
        rank: null as number | null,
        isFollowing: user ? followingIds.has(book.user.id) : false,
        isSubscribed: user ? subscribedIds.has(book.user.id) : false,
        reactions: bookReactions[book.id] || {},
        userReaction: userBookReactions[book.id] || null
      })),
      ...filteredRecipes.map(r => ({
        id: r.id,
        title: r.title,
        description: r.description,
        created_at: r.created_at,
        is_free: r.is_free,
        price_cents: (r as any).price_cents ?? null,
        cooked_count: (r as any).cooked_count ?? null,
        cover_photo_url: r.cover_photo_url,
        recipe_photos: (r as any).recipe_photos || [],
        user: r.users,
        rank: null as number | null, // placeholder until we wire cook-event rank here
        type: 'recipe' as const,
        isFollowing: user ? followingIds.has(r.users.id) : false,
        isSubscribed: user ? subscribedIds.has(r.users.id) : false,
        reactions: {},
        userReaction: null
      }))
    ]

    // Attach recipe rank to match the recipe page logic (recent 7d total + 1h boost), and fetch reactions
    try {
      const recipeIds = filteredRecipes.map((r: any) => r.id)
      if (recipeIds.length > 0) {
        const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString()

        // Recent 7d and 1h cooks
        const { data: cookEventsAll } = await supabase
          .from('recipe_cook_events' as any)
          .select('recipe_id, created_at')
          .in('recipe_id', recipeIds)
          .gte('created_at', oneWeekAgo)

        const { data: hourlyCooks } = await supabase
          .from('recipe_cook_events' as any)
          .select('recipe_id')
          .in('recipe_id', recipeIds)
          .gte('created_at', oneHourAgo)

        const totalMap = (cookEventsAll || []).reduce((acc: Record<string, number>, e: any) => {
          acc[e.recipe_id] = (acc[e.recipe_id] || 0) + 1
          return acc
        }, {})
        const hourlyMap = (hourlyCooks || []).reduce((acc: Record<string, number>, e: any) => {
          acc[e.recipe_id] = (acc[e.recipe_id] || 0) + 1
          return acc
        }, {})

        const items = recipeIds.map((id: string) => ({ id, total: totalMap[id] || 0, hourly: hourlyMap[id] || 0 }))
        const hasAny = items.some(i => i.total > 0 || i.hourly > 0)
        const ranked = hasAny
          ? items.sort((a, b) => (b.total - a.total) || (b.hourly - a.hourly))
          : items

        const rankMap = new Map<string, number>()
        ranked.forEach((r, idx) => rankMap.set(r.id, idx + 1))

        // Reactions for recipes
        const { data: recipeReacts } = await supabase
          .from('reactions')
          .select('recipe_id, reaction_type, user_id')
          .in('recipe_id', recipeIds)

        const recipeReactions: Record<string, Record<string, number>> = {}
        const userRecipeReactions: Record<string, string> = {}

        recipeReacts?.forEach((reaction: any) => {
          if (!recipeReactions[reaction.recipe_id]) recipeReactions[reaction.recipe_id] = {}
          recipeReactions[reaction.recipe_id][reaction.reaction_type] = (recipeReactions[reaction.recipe_id][reaction.reaction_type] || 0) + 1
          if (user && reaction.user_id === user.id) userRecipeReactions[reaction.recipe_id] = reaction.reaction_type
        })

        combinedPosts = combinedPosts.map(p =>
          p.type === 'recipe'
            ? {
                ...p,
                rank: rankMap.get(p.id) || null,
                reactions: recipeReactions[p.id] || {},
                userReaction: userRecipeReactions[p.id] || null
              }
            : p
        )
      }
    } catch (e) {
      console.warn('recipe rank/reactions attach failed', e)
    }


    // Attach book rank from daily_bestsellers if available (fallback to projects.bestseller_rank if present)
    try {
      const bookIds = filteredBookReleases.map((b: any) => b.id)
      if (bookIds.length > 0) {
        const today = new Date()
        const dateStr = today.toISOString().slice(0, 10)
        // Try today's daily bestsellers; fallback to latest available date by selection if needed
        const { data: daily } = await supabase
          .from('daily_bestsellers' as any)
          .select('project_id, rank')
          .in('project_id', bookIds)
          .eq('date', dateStr)

        const rankMap = new Map<string, number>()
        daily?.forEach((row: any) => rankMap.set(row.project_id, row.rank))

        // Fallback to projects.bestseller_rank if any are missing
        const missing = bookIds.filter(id => !rankMap.has(id))
        if (missing.length > 0) {
          const { data: projectsRows } = await supabase
            .from('projects' as any)
            .select('id, bestseller_rank')
            .in('id', missing)
          projectsRows?.forEach((row: any) => {
            if (row.bestseller_rank) rankMap.set(row.id, row.bestseller_rank)
          })
        }

        combinedPosts = combinedPosts.map(p =>
          p.type === 'book' ? { ...p, rank: rankMap.get(p.id) || null } : p
        )
      }
    } catch (e) {
      console.warn('book rank attach failed', e)
    }

    // Integrate Duo: fetch duo timeline posts and merge
    try {
      // Build absolute URL from the incoming request origin to satisfy fetch() in RSC/edge
      const origin = request.headers.get('x-forwarded-proto') && request.headers.get('x-forwarded-host')
        ? `${request.headers.get('x-forwarded-proto')}://${request.headers.get('x-forwarded-host')}`
        : new URL(request.url).origin
      const resp = await fetch(`${origin}/api/duo/timeline`, { cache: 'no-store' })
      if (resp.ok) {
        const { posts: duoPosts } = await resp.json()
        const mapped = (duoPosts || []).map((p: any) => ({
          ...p,
          type: 'duo' as const,
        }))
        combinedPosts = [...combinedPosts, ...mapped]
      }
    } catch (e) {
      console.warn('Timeline: failed to load duo posts', e)
    }

    // Sort purely by recency across all types (no quotas, no special casing)
    combinedPosts.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

    // Apply offset and limit to combined results
    const paginatedPosts = combinedPosts.slice(offset, offset + limit)

    return NextResponse.json({
      posts: paginatedPosts,
      hasMore: combinedPosts.length > offset + limit
    })
  } catch (error) {
    console.error('Timeline API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
