import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/server'
import { checkRateLimit, createRateLimitHeaders, createRateLimitResponse } from '@/lib/rateLimit'

// POST /api/duo/invite { duoPostId, invitee_user_id, reserve?: boolean }
export async function POST(request: NextRequest) {
  try {
    const rate = checkRateLimit(request, 'WRITE', 'duo-invite')
    if (!rate.allowed) return createRateLimitResponse('WRITE', rate)

    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    const { duoPostId, invitee_user_id, reserve } = await request.json()
    if (!duoPostId || !invitee_user_id) return NextResponse.json({ error: 'Missing fields' }, { status: 400 })

    // Ensure duo exists and current user is initiator
    const { data: duo } = await supabase
      .from('duo_posts')
      .select('id, initiator_user_id, responder_user_id, status')
      .eq('id', duoPostId)
      .maybeSingle()
    if (!duo) return NextResponse.json({ error: 'Duo not found' }, { status: 404 })
    if (duo.initiator_user_id !== user.id) return NextResponse.json({ error: 'Forbidden' }, { status: 403 })

    // Optionally reserve the duo for this friend (set responder_user_id)
    if (reserve && !duo.responder_user_id) {
      await supabase
        .from('duo_posts')
        .update({ responder_user_id: invitee_user_id })
        .eq('id', duoPostId)
    }

    // Create in-app notification
    await supabase
      .from('notifications')
      .insert({
        user_id: invitee_user_id,
        type: 'duo_invite',
        title: "You've been invited to an OnlyDuo",
        body: 'Tap to record Part B. It takes 15 seconds.',
        data: { duo_post_id: duoPostId, url: `/duo/respond/${duoPostId}` }
      })

    const headers = createRateLimitHeaders(rate)
    return NextResponse.json({ ok: true }, { headers })
  } catch (e: any) {
    console.error('duo/invite error', e)
    return NextResponse.json({ error: 'Failed to send invite' }, { status: 500 })
  }
}

