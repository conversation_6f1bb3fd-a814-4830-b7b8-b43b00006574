import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { checkRateLimit, createRateLimitHeaders, createRateLimitResponse } from '@/lib/utils/rate-limit'

// POST /api/duo/commitPart
// body: { duoPostId: string, partNumber: 1|2, media: jsonb }
export async function POST(request: NextRequest) {
  try {
    const rate = checkRateLimit(request, 'WRITE', 'duo-commit-part')
    if (!rate.allowed) return createRateLimitResponse('WRITE', rate)

    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    const { duoPostId, partNumber, media } = await request.json()
    if (!duoPostId || ![1,2].includes(partNumber)) {
      return NextResponse.json({ error: 'duoPostId and valid partNumber required' }, { status: 400 })
    }

    // Fetch duo and determine author for this part
    const { data: duo } = await supabase
      .from('duo_posts')
      .select('id, initiator_user_id, responder_user_id, status')
      .eq('id', duoPostId)
      .single()

    if (!duo) return NextResponse.json({ error: 'Duo not found' }, { status: 404 })

    const expectedAuthorId = partNumber === 1 ? duo.initiator_user_id : (duo.responder_user_id ?? user.id)
    if (user.id !== expectedAuthorId) {
      // Allow first-time responder to claim responder_user_id
      if (partNumber === 2 && !duo.responder_user_id) {
        const { error: updErr } = await supabase
          .from('duo_posts')
          .update({ responder_user_id: user.id })
          .eq('id', duoPostId)
        if (updErr) return NextResponse.json({ error: 'Failed to bind responder' }, { status: 500 })
      } else {
        return NextResponse.json({ error: 'Not authorized for this part' }, { status: 403 })
      }
    }

    // Upsert the part with media
    const { error: upErr } = await supabase
      .from('duo_parts')
      .upsert({
        duo_post_id: duoPostId,
        part_number: partNumber,
        author_user_id: user.id,
        media
      }, {
        onConflict: 'duo_post_id,part_number'
      })

    if (upErr) return NextResponse.json({ error: 'Failed to save part', details: upErr.message }, { status: 500 })

    // If part 2 saved, require initiator approval before going live
    if (partNumber === 2) {
      const { error: updErr2 } = await supabase
        .from('duo_posts')
        .update({ status: 'awaiting_approval' })
        .eq('id', duoPostId)
      if (updErr2) return NextResponse.json({ error: 'Failed to set awaiting approval' }, { status: 500 })

      // Hide timeline card until approval (no-op if row absent)
      await supabase
        .from('timeline_posts')
        .update({ is_hidden: true })
        .eq('duo_post_id', duoPostId)

      // Notify initiator via Correspondence notifications
      await supabase.from('notifications').insert({
        user_id: duo.initiator_user_id,
        type: 'duo_response_ready',
        title: 'Your Duo has a new response',
        body: 'Review and approve your Duo to publish it to your timeline',
        data: { duo_post_id: duoPostId, url: `/duo/${duoPostId}` }
      })
    }

    const headers = createRateLimitHeaders(rate)
    return NextResponse.json({ ok: true }, { headers })
  } catch (e: any) {
    console.error('duo/commitPart error', e)
    return NextResponse.json({ error: 'Failed to commit part' }, { status: 500 })
  }
}

