"use client"

import { useEffect, useMemo, useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { useSearchParams } from "next/navigation"
import { Day1Badge } from "@/components/Day1Badge"
import { createSupabaseClient } from "@/lib/supabase/client"


interface Recipe {
  id: string
  title: string
  description?: string
  cover_photo_url?: string | null
  is_free: boolean
  price_cents?: number | null
  cooked_count?: number | null
  created_at: string
  users?: {
    id: string
    name: string
    profile_picture_url?: string | null
    has_day1_badge?: boolean
    signup_number?: number | null
    badge_tier?: string | null
  }
}

export default function RecipesPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <div className="text-center">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-serif text-gray-900 mb-3 sm:mb-4">
              🍳 OnlyDiary Recipes
            </h1>
            <p className="text-sm sm:text-lg text-gray-600 max-w-2xl mx-auto px-2">
              Discover recipes from creators across OnlyDiary — search, explore, and cook something new.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <RecipesPageContent />
      </div>
    </div>
  )
}

function RecipesPageContent() {
  const supabase = createSupabaseClient()
  const searchParams = useSearchParams()

  const [recipes, setRecipes] = useState<Recipe[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [searchQuery, setSearchQuery] = useState<string>(searchParams.get('search') || "")
  const [sortBy, setSortBy] = useState<string>('popular')

  useEffect(() => {
    fetchRecipes()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery, sortBy])

  const fetchRecipes = async () => {
    setLoading(true)
    try {
      let query = supabase
        .from('recipes' as any)
        .select(`
          id,
          title,
          description,
          cover_photo_url,
          is_free,
          price_cents,
          cooked_count,
          created_at,
          users!inner(id, name, profile_picture_url, has_day1_badge, signup_number, badge_tier)
        `)
        .eq('is_hidden', false)

      if (searchQuery.trim()) {
        const q = searchQuery.trim()
        query = query.or(`title.ilike.%${q}%,description.ilike.%${q}%`)
      }

      switch (sortBy) {
        case 'popular':
          query = query.order('cooked_count', { ascending: false }).order('created_at', { ascending: false })
          break
        case 'newest':
          query = query.order('created_at', { ascending: false })
          break
        case 'free':
          query = query.order('is_free', { ascending: false }).order('created_at', { ascending: false })
          break
        case 'price_low':
          query = query.order('price_cents', { ascending: true }).order('created_at', { ascending: false })
          break
      }

      const { data, error } = await query.limit(60)
      if (error) throw error

      // Ensure we always have a photo when available: fallback to first recipe_photos image
      let items: any[] = (data as any) || []
      const missingIds = items.filter((r: any) => !r.cover_photo_url).map((r: any) => r.id)
      if (missingIds.length > 0) {
        const { data: photos } = await supabase
          .from('recipe_photos' as any)
          .select('recipe_id, url, created_at')
          .in('recipe_id', missingIds)
          .order('created_at', { ascending: true })

        const firstPhotoByRecipe: Record<string, string> = {}
        for (const p of photos || []) {
          if (!firstPhotoByRecipe[p.recipe_id]) firstPhotoByRecipe[p.recipe_id] = p.url
        }
        items = items.map((r: any) => ({
          ...r,
          cover_photo_url: r.cover_photo_url || firstPhotoByRecipe[r.id] || null,
        }))
      }

      setRecipes(items)
    } catch (e) {
      console.error('Failed to fetch recipes', e)
      setRecipes([])
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="recipes-page">
      {/* Controls */}
      <div className="mb-6 grid gap-3 sm:grid-cols-3 items-center">
        <div className="sm:col-span-2">
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search recipes…"
              className="w-full pl-10 pr-4 py-2.5 bg-white border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-800 placeholder:text-gray-500"
            />
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">🔎</span>
          </div>
        </div>
        <div className="flex items-center gap-2 justify-end">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="border border-gray-200 bg-white rounded-lg px-3 py-2 text-sm text-gray-800"
          >
            <option value="popular">🔥 Most Cooked</option>
            <option value="newest">🆕 Newest</option>
            <option value="free">🟢 Free first</option>
            <option value="price_low">💰 Price: Low to High</option>
          </select>
        </div>
      </div>

      {/* Grid */}
      {loading ? (
        <div className="text-center py-16 text-gray-600">Loading…</div>
      ) : recipes.length === 0 ? (
        <div className="text-center py-16">
          <div className="text-6xl mb-4">🍳</div>
          <h3 className="text-xl font-medium text-gray-900 mb-2">No recipes found</h3>
          <p className="text-gray-600">Try a different search or check back later.</p>
        </div>
      ) : (
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {recipes.map((r) => (
            <Link key={r.id} href={`/recipes/${r.id}`} className="group block bg-white rounded-xl overflow-hidden border border-gray-100 hover:shadow-md transition-all">
              <div className="aspect-video bg-gray-50 relative">
                {r.cover_photo_url ? (
                  <Image src={r.cover_photo_url} alt={r.title} fill className="object-cover" />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-3xl">🍳</div>
                )}
              </div>
              <div className="p-3">
                <h3 className="font-medium text-gray-900 line-clamp-2 mb-1">{r.title}</h3>
                <div className="text-xs text-gray-500 line-clamp-2 mb-2">{r.description || ''}</div>
                <div className="flex items-center justify-between text-xs text-gray-600">
                  <div className="flex items-center gap-2 min-w-0">
                    <div className="w-6 h-6 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                      {r.users?.profile_picture_url ? (
                        // eslint-disable-next-line @next/next/no-img-element
                        <img src={r.users.profile_picture_url} alt={r.users.name} className="w-full h-full object-cover" />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-[10px]">{(r.users?.name || 'A').charAt(0).toUpperCase()}</div>
                      )}
                    </div>
                    <span className="truncate flex items-center gap-1">
                      {r.users?.name || 'Unknown'}
                      {r.users?.has_day1_badge && (
                        <Day1Badge signupNumber={r.users?.signup_number || undefined} badgeTier={r.users?.badge_tier || undefined} size="sm" clickable={false} />
                      )}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    {r.is_free ? (
                      <span className="px-2 py-0.5 rounded bg-gray-100 text-gray-700">FREE</span>
                    ) : (
                      <span className="px-2 py-0.5 rounded bg-purple-100 text-purple-700">${((r.price_cents || 0) / 100).toFixed(2)}</span>
                    )}
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}
    </div>
  )
}

