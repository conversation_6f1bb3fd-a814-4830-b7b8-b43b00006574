import { createSupabaseServerClient } from "@/lib/supabase/client"
import { notFound } from "next/navigation"
import { UnifiedProfileClient } from "@/components/UnifiedProfileClient"
import { GlobalSearchBar } from "@/components/GlobalSearchBar"


interface UnifiedProfilePageProps {
  params: Promise<{
    id: string
  }>
}

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const searchBar = (
  <div className="max-w-4xl mx-auto px-4 sm:px-6 py-4">
    <GlobalSearchBar />
  </div>
)


export default async function UnifiedProfilePage({ params }: UnifiedProfilePageProps) {
  const { id } = await params
  const supabase = await createSupabaseServerClient()

  // Check if current user is authenticated and get subscription status
  const { data: { user } } = await supabase.auth.getUser()

  // Check if user has active subscription to this writer
  let hasActiveSubscription = false
  let isFollowing = false
  if (user) {
    const { data: subscription } = await supabase
      .from("subscriptions")
      .select("id, current_period_end")
      .eq("reader_id", user.id)
      .eq("writer_id", id)
      .eq("status", "active")
      .gte("current_period_end", new Date().toISOString())
      .single()

    hasActiveSubscription = !!subscription

    // Check if user is following this writer
    const { data: follow } = await supabase
      .from("follows")
      .select("id")
      .eq("follower_id", user.id)
      .eq("writer_id", id)
      .single()

    isFollowing = !!follow
  }

  // Get writer data directly from users table first
  const { data: writerData, error: writerError } = await supabase
    .from('users')
    .select('id, name, avatar, profile_picture_url, bio, price_monthly, role, custom_url, subscriber_count, follower_count, stripe_account_id, has_day1_badge, signup_number')
    .eq('id', id)
    .single()

  console.log('Profile query result - writerData:', writerData)
  console.log('Profile query result - badge fields:', {
    has_day1_badge: writerData?.has_day1_badge,
    signup_number: writerData?.signup_number,
    name: writerData?.name
  })

  if (writerError || !writerData) {
    console.error('Writer lookup error:', writerError)
    notFound()
  }

  // All authenticated users can have profiles in the unified system
  if (writerData.role !== 'user' && writerData.role !== 'admin') {
    notFound()
  }

  // Get free entry for this writer
  const { data: freeEntry } = await supabase
    .from('diary_entries')
    .select('id, title, body_md, created_at')
    .eq('user_id', id)
    .eq('is_free', true)
    .eq('is_hidden', false)
    .single()

  // Use the raw user data for UnifiedProfileClient
  const writer = {
    id: writerData.id,
    name: writerData.name,
    avatar: writerData.avatar,
    profile_picture_url: writerData.profile_picture_url,
    bio: writerData.bio,
    price_monthly: writerData.price_monthly,
    stripe_account_id: writerData.stripe_account_id,
    subscriber_count: writerData.subscriber_count,
    follower_count: writerData.follower_count || 0,
    custom_url: writerData.custom_url,
    has_day1_badge: writerData.has_day1_badge,
    signup_number: writerData.signup_number,
    badge_tier: writerData.badge_tier,
    free_entry_id: freeEntry?.id || null,
    free_entry_title: freeEntry?.title || null,
    free_entry_body_md: freeEntry?.body_md || null,
    free_entry_created_at: freeEntry?.created_at || null
  }

  // Get all diary entries (free and locked) for this writer
  const { data: allEntries, error: entriesError } = await supabase
    .from("diary_entries")
    .select("id, title, body_md, is_free, view_count, created_at")
    .eq("user_id", id)
    .eq("is_hidden", false)
    .order("created_at", { ascending: false })

  if (entriesError) {
    console.error('Error fetching entries:', entriesError)
  }

  // Get all book projects for this writer
  const { data: projects, error: projectsError } = await supabase
    .from("projects")
    .select(`
      id,
      title,
      description,
      cover_image_url,
      genre,
      is_private,
      is_complete,
      price_type,
      price_amount,
      total_chapters,
      total_words,
      sales_count,
      bestseller_rank,
      created_at,
      updated_at
    `)
    .eq("user_id", id)
    .eq("is_private", false)
    .order("updated_at", { ascending: false })

  if (projectsError) {
    console.error('Error fetching projects:', projectsError)
  }

  // Get all audio posts for this writer with reactions
  const { data: audioPosts, error: audioError } = await supabase
    .from("audio_posts")
    .select(`
      id,
      audio_url,
      description,
      duration_seconds,
      love_count,
      reply_count,
      created_at,
      user:users!user_id (
        id,
        name,
        avatar,
        profile_picture_url,
        has_day1_badge,
        signup_number
      )
    `)
    .eq("user_id", id)
    .order("created_at", { ascending: false })

  // Get reactions for audio posts
  const audioPostsWithReactions = await Promise.all(
    (audioPosts || []).map(async (post) => {
      // Get reaction counts
      const { data: reactionCounts } = await supabase
        .rpc('get_audio_reaction_counts', { post_id: post.id })

      // Get user's reaction if logged in
      const { data: userReactionData } = user ? await supabase
        .from('reactions')
        .select('reaction_type')
        .eq('audio_post_id', post.id)
        .eq('user_id', user.id)
        .single() : { data: null }

      // Convert reaction counts to object
      const reactions = reactionCounts?.reduce((acc: Record<string, number>, item: any) => {
        acc[item.reaction_type] = item.count
        return acc
      }, {}) || {}

      return {
        ...post,
        reactions,
        userReaction: userReactionData?.reaction_type || null
      }
    })
  )

  if (audioError) {
    console.error('Error fetching audio posts:', audioError)
  }


  // Load recipes for this writer (ensure we always have a photo if available)
  const { data: recipesRaw } = await supabase
    .from('recipes' as any)
    .select('id, title, description, cover_photo_url, is_free, created_at')
    .eq('user_id', id)
    .eq('is_hidden', false)
    .order('created_at', { ascending: false })

  let recipes = recipesRaw || []

  // Fallback: if cover_photo_url is missing, pull the first recipe photo
  const missingPhotoIds = recipes.filter((r: any) => !r.cover_photo_url).map((r: any) => r.id)
  if (missingPhotoIds.length > 0) {
    const { data: photos } = await supabase
      .from('recipe_photos' as any)
      .select('recipe_id, url, created_at')
      .in('recipe_id', missingPhotoIds)
      .order('created_at', { ascending: true })

    const firstPhotoByRecipe: Record<string, string> = {}
    for (const p of photos || []) {
      if (!firstPhotoByRecipe[p.recipe_id]) firstPhotoByRecipe[p.recipe_id] = p.url
    }

    recipes = recipes.map((r: any) => ({
      ...r,
      cover_photo_url: r.cover_photo_url || firstPhotoByRecipe[r.id] || null,
    }))
  }

  return (
    <>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 pt-4">
        <GlobalSearchBar />
      </div>
      <UnifiedProfileClient
        user={writer}
        diaryEntries={allEntries || []}
        projects={projects || []}
        audioPosts={audioPostsWithReactions || []}
        recipes={recipes || []}
        hasActiveSubscription={hasActiveSubscription}
        isFollowing={isFollowing}
        isOwnProfile={user?.id === id}
        currentUserId={user?.id}
      />
    </>
  )
}