'use client'

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"

export default function WriteSelection() {
  const [user, setUser] = useState<unknown>(null)
  const [loading, setLoading] = useState(true)
  const [navigating, setNavigating] = useState<'diary' | 'book' | 'audio' | 'recipe' | 'duo' | null>(null)


  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user: authUser }, error } = await supabase.auth.getUser()

      if (error || !authUser) {
        router.push('/login')
        return
      }

      // Get user profile
      const { data: profile } = await supabase
        .from("users")
        .select("*")
        .eq("id", authUser.id)
        .single()

      if (!profile) {
        router.push('/login')
        return
      }

      // All authenticated users can write (unified experience)
      if (profile.role !== 'user' && profile.role !== 'admin') {
        router.push('/timeline')
        return
      }

      setUser(profile)
      setLoading(false)
    }

    checkAuth()
  }, [router, supabase])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-gray-600 font-serif">Loading...</div>
      </div>
    )
  }

  if (!user) return null

  const handleNavigation = (type: 'diary' | 'book' | 'audio' | 'recipe' | 'duo', path: string) => {
    setNavigating(type)
    router.push(path)
  }



  return (
    <div className="min-h-screen bg-white relative">
      {navigating && (
        <div className="absolute inset-0 bg-white/50 z-10 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 shadow-lg border border-gray-200">
            <div className="flex items-center gap-3">
              <div className="animate-spin w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full"></div>
              <span className="text-gray-700 font-serif">
                {navigating === 'diary' ? 'Opening diary editor...' :
                 navigating === 'book' ? 'Opening book project editor...' :
                 navigating === 'audio' ? 'Opening audio recorder...' : 'Loading...'}
              </span>
            </div>
          </div>
        </div>
      )}
      <div className="max-w-5xl mx-auto px-4 sm:px-6 py-8 sm:py-12">

        {/* Clean Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-serif text-gray-900 mb-6">
            What would you like to create?
          </h1>
          <p className="text-gray-600 font-serif">
            Choose a format to send to your timeline.
          </p>
        </div>

        {/* Clean Writing Options */}
        <div className="space-y-4">

          {/* Options Grid - mobile-first and responsive */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Diary Entry */}
            <button
              onClick={() => handleNavigation('diary', '/write/diary')}
              disabled={navigating !== null}
              className="group w-full text-left cursor-pointer"
            >
              <div className={`relative overflow-hidden rounded-2xl p-6 sm:p-8 transition-all border min-h-[136px] ${
                navigating === null ? 'hover:shadow-lg' : ''
              } ${navigating === 'diary' ? 'border-gray-300 bg-gray-50' : 'border-gray-200 hover:border-gray-300 bg-white'}`}>
                <div className="flex items-center gap-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-blue-50 text-2xl">📔</div>
                  <div className="flex-1">
                    <h2 className="text-xl font-serif text-gray-900 mb-1 sm:mb-2">Diary Entry</h2>
                    <p className="text-gray-600 font-serif text-sm sm:text-base">Share your daily thoughts and experiences</p>
                  </div>
                  <div className="text-gray-400 group-hover:text-gray-600 transition-all">
                    {navigating === 'diary' ? (
                      <div className="animate-spin w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                    ) : (
                      <>
                        {/* Mobile: purple arrow */}
                        <div className="sm:hidden inline-flex items-center justify-center h-8 w-8 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-sm">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                        {/* Desktop: neutral arrow circle */}
                        <div className="hidden sm:flex h-8 w-8 rounded-full border border-gray-200 items-center justify-center group-hover:border-gray-300">
                          <svg className="w-4 h-4 transition-transform group-hover:translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </button>

            {/* Write a Book (Editor) */}
            <button
              onClick={() => handleNavigation('book', '/write/projects')}
              disabled={navigating !== null}
              className="group w-full text-left cursor-pointer"
            >
              <div className={`relative overflow-hidden rounded-2xl p-6 sm:p-8 transition-all border min-h-[136px] ${
                navigating === null ? 'hover:shadow-lg' : ''
              } ${navigating === 'book' ? 'border-purple-300 bg-purple-50' : 'border-gray-200 hover:border-gray-300 bg-white'}`}>
                <div className="flex items-center gap-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-purple-50 text-2xl">✏️</div>
                  <div className="flex-1">
                    <h2 className="text-xl font-serif text-gray-900 mb-1 sm:mb-2">Write a Book (Editor)</h2>
                    <p className="text-gray-600 font-serif text-sm sm:text-base">Draft chapters in our editor; publish when ready</p>
                  </div>
                  <div className="text-gray-400 group-hover:text-gray-600 transition-all">
                    {navigating === 'book' ? (
                      <div className="animate-spin w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                    ) : (
                      <>
                        {/* Mobile: purple arrow */}
                        <div className="sm:hidden inline-flex items-center justify-center h-8 w-8 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-sm">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                        {/* Desktop: neutral arrow circle */}
                        <div className="hidden sm:flex h-8 w-8 rounded-full border border-gray-200 items-center justify-center group-hover:border-gray-300">
                          <svg className="w-4 h-4 transition-transform group-hover:translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </button>

            {/* Upload Book (EPUB/PDF) */}
            <button
              onClick={() => handleNavigation('book', '/write/upload-ebook')}
              disabled={navigating !== null}
              className="group w-full text-left cursor-pointer"
            >
              <div className={`relative overflow-hidden rounded-2xl p-6 sm:p-8 transition-all border min-h-[136px] ${
                navigating === null ? 'hover:shadow-lg' : ''
              } ${navigating === 'book' ? 'border-purple-300 bg-purple-50' : 'border-gray-200 hover:border-gray-300 bg-white'}`}>
                <div className="flex items-center gap-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-purple-50 text-2xl">📖</div>
                  <div className="flex-1">
                    <h2 className="text-xl font-serif text-gray-900 mb-1 sm:mb-2">Upload Book (EPUB/PDF)</h2>
                    <p className="text-gray-600 font-serif text-sm sm:text-base">Import an existing ebook and publish it</p>
                  </div>
                  <div className="text-gray-400 group-hover:text-gray-600 transition-all">
                    {navigating === 'book' ? (
                      <div className="animate-spin w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                    ) : (
                      <>
                        {/* Mobile: purple arrow */}
                        <div className="sm:hidden inline-flex items-center justify-center h-8 w-8 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-sm">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                        {/* Desktop: neutral arrow circle */}
                        <div className="hidden sm:flex h-8 w-8 rounded-full border border-gray-200 items-center justify-center group-hover:border-gray-300">
                          <svg className="w-4 h-4 transition-transform group-hover:translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </button>

            {/* Audio Post */}
            <button
              onClick={() => handleNavigation('audio', '/audio/create')}
              disabled={navigating !== null}
              className="group w-full text-left cursor-pointer"
            >
              <div className={`relative overflow-hidden rounded-2xl p-6 sm:p-8 transition-all border min-h-[136px] ${
                navigating === null ? 'hover:shadow-lg' : ''
              } ${navigating === 'audio' ? 'border-blue-300 bg-blue-50' : 'border-gray-200 hover:border-gray-300 bg-white'}`}>
                <div className="flex items-center gap-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-blue-50 text-2xl">🎵</div>
                  <div className="flex-1">
                    <h2 className="text-xl font-serif text-gray-900 mb-1 sm:mb-2">Audio Post</h2>
                    <p className="text-gray-600 font-serif text-sm sm:text-base">Record a 9-second audio message</p>
                  </div>
                  <div className="text-gray-400 group-hover:text-gray-600 transition-all">
                    {navigating === 'audio' ? (
                      <div className="animate-spin w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                    ) : (
                      {/* Mobile: purple arrow */}
                      <div className="sm:hidden inline-flex items-center justify-center h-8 w-8 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-sm">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                      {/* Desktop: neutral arrow circle */}
                      <div className="hidden sm:flex h-8 w-8 rounded-full border border-gray-200 items-center justify-center group-hover:border-gray-300">
                        <svg className="w-4 h-4 transition-transform group-hover:translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </button>

            {/* OnlyDuo */}
            <button
              onClick={() => handleNavigation('duo', '/duo/new')}
              disabled={navigating !== null}
              className="group w-full text-left cursor-pointer"
            >
              <div className={`relative overflow-hidden rounded-2xl p-6 sm:p-8 transition-all border min-h-[136px] ${
                navigating === null ? 'hover:shadow-lg' : ''
              } ${navigating === 'duo' ? 'border-pink-300 bg-pink-50' : 'border-gray-200 hover:border-gray-300 bg-white'}`}>
                <div className="flex items-center gap-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-pink-50 text-2xl">🎬</div>
                  <div className="flex-1">
                    <h2 className="text-xl font-serif text-gray-900 mb-1 sm:mb-2">OnlyDuo</h2>
                    <p className="text-gray-600 font-serif text-sm sm:text-base">Start a two-part video conversation</p>
                  </div>
                  <div className="text-gray-400 group-hover:text-gray-600 transition-all">
                    {navigating === 'duo' ? (
                      <div className="animate-spin w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                    ) : (
                      {/* Mobile: purple arrow */}
                      <div className="sm:hidden inline-flex items-center justify-center h-8 w-8 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-sm">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                      {/* Desktop: neutral arrow circle */}
                      <div className="hidden sm:flex h-8 w-8 rounded-full border border-gray-200 items-center justify-center group-hover:border-gray-300">
                        <svg className="w-4 h-4 transition-transform group-hover:translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </button>


              {/* Recipes */}
              <button
                onClick={() => handleNavigation('recipe', '/write/recipes')}
                disabled={navigating !== null}
                className="group w-full text-left cursor-pointer h-full"
              >
                <div className={`relative overflow-hidden rounded-2xl p-6 sm:p-8 transition-all border min-h-[136px] ${
                  navigating === null ? 'hover:shadow-lg' : ''
                } ${navigating === 'recipe' ? 'border-green-300 bg-green-50' : 'border-gray-200 hover:border-gray-300 bg-white'}`}>
                  <div className="flex items-center gap-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-green-50 text-2xl">🍳</div>
                    <div className="flex-1">
                      <h2 className="text-xl font-serif text-gray-900 mb-1 sm:mb-2">Recipes</h2>
                      <p className="text-gray-600 font-serif text-sm sm:text-base">Share delicious recipes with photos, video, and ingredients</p>
                    </div>
                    <div className="text-gray-400 group-hover:text-gray-600 transition-all">
                      {navigating === 'recipe' ? (
                        <div className="animate-spin w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                      ) : (
                        {/* Mobile: purple arrow */}
                        <div className="sm:hidden inline-flex items-center justify-center h-8 w-8 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-sm">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                        {/* Desktop: neutral arrow circle */}
                        <div className="hidden sm:flex h-8 w-8 rounded-full border border-gray-200 items-center justify-center group-hover:border-gray-300">
                          <svg className="w-4 h-4 transition-transform group-hover:translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </button>




          </div>




        </div>
      </div>
    </div>
  )
}
