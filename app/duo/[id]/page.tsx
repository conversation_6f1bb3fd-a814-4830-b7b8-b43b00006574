import { createServerSupabaseClient } from '@/lib/supabase/server'
import { DuoApproveClient } from '@/components/duo/DuoApproveClient'
import { DuoVideoPlayer } from '@/components/duo/DuoVideoPlayer'
import Image from 'next/image'
import Link from 'next/link'
import { redirect } from 'next/navigation'

export default async function DuoApprovePage({ params }: { params: { id: string } }) {
  const supabase = await createServerSupabaseClient()
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login?next=' + encodeURIComponent(`/duo/${params.id}`))

  // Load duo, parts, and users
  const { data: duo } = await supabase
    .from('duo_posts')
    .select(`
      id, initiator_user_id, responder_user_id, status, title, body,
      initiator:users!duo_posts_initiator_user_id_fkey(id, name, profile_picture_url, avatar),
      responder:users!duo_posts_responder_user_id_fkey(id, name, profile_picture_url, avatar)
    `)
    .eq('id', params.id)
    .maybeSingle()

  if (!duo) {
    return (
      <div className="max-w-xl mx-auto p-6">
        <h1 className="text-xl font-semibold mb-2">Duo not found</h1>
        <p className="text-gray-600">The Duo you are trying to view does not exist.</p>
      </div>
    )
  }

  const { data: parts } = await supabase
    .from('duo_parts')
    .select('duo_post_id, part_number, author_user_id, media')
    .eq('duo_post_id', params.id)

  const partA = parts?.find(p => p.part_number === 1)?.media || null
  const partB = parts?.find(p => p.part_number === 2)?.media || null

  const isInitiator = duo.initiator_user_id === user.id

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="flex items-center gap-3 mb-4">
        <Link href={`/u/${duo.initiator?.id || duo.initiator_user_id}`} className="w-10 h-10 rounded-full overflow-hidden bg-gray-100 flex-shrink-0">
          {(duo.initiator?.profile_picture_url || duo.initiator?.avatar) ? (
            <Image src={(duo.initiator?.profile_picture_url || duo.initiator?.avatar) as string} alt={duo.initiator?.name || 'User'} width={40} height={40} className="w-full h-full object-cover" />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-500">{(duo.initiator?.name || 'U')[0]}</div>
          )}
        </Link>
        <div>
          <div className="text-sm text-gray-600">Duo by</div>
          <Link href={`/u/${duo.initiator?.id || duo.initiator_user_id}`} className="font-medium text-gray-900 hover:text-blue-600">
            {duo.initiator?.name || 'User'}
          </Link>
        </div>
      </div>

      <h1 className="text-xl font-semibold mb-2">{duo.title || 'A Duo'}</h1>
      {duo.body && <p className="text-gray-600 mb-4">{duo.body}</p>}

      {duo.status === 'awaiting_approval' && (
        <div className="mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-3 text-sm text-yellow-900">
          Awaiting approval — Only the initiator can approve this Duo to publish it on the timeline.
        </div>
      )}

      {partA ? (
        <DuoVideoPlayer partA={partA} partB={partB || undefined} />
      ) : (
        <div className="rounded bg-gray-50 border border-gray-200 p-4 mb-4">Waiting for Part A.</div>
      )}

      <DuoApproveClient
        duoPostId={duo.id}
        isInitiator={isInitiator}
        hasPartB={!!partB}
        status={duo.status}
      />
    </div>
  )
}

