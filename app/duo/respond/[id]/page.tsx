import { createServerSupabaseClient } from '@/lib/supabase/server'
import { DuoRespondClient } from '@/components/duo/DuoRespondClient'
import { redirect } from 'next/navigation'

export default async function DuoRespondPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  const supabase = await createServerSupabaseClient()
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login?next=' + encodeURIComponent(`/duo/respond/${id}`))

  // Fetch the duo post
  const { data: duo } = await supabase
    .from('duo_posts')
    .select('id, initiator_user_id, responder_user_id, status, title, body, initiator:users!duo_posts_initiator_user_id_fkey(id, name)')
    .eq('id', id)
    .single()

  if (!duo) {
    return (
      <div className="max-w-xl mx-auto p-6">
        <h1 className="text-xl font-semibold mb-2">Duo not found</h1>
        <p className="text-gray-600">The Duo you are trying to respond to does not exist.</p>
      </div>
    )
  }

  // Determine if current user may upload Part B
  const canUpload = !duo.responder_user_id || duo.responder_user_id === user.id

  return (
    <div className="min-h-[60vh]">
      <DuoRespondClient
        duoPostId={duo.id}
        initiatorName={duo.initiator?.name || undefined}
        canUpload={canUpload}
        status={duo.status as any}
      />
    </div>
  )
}

